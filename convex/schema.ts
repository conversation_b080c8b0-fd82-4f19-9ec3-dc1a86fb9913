import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";

export default defineSchema({
  libraryItems: defineTable({
    // Basic item information
    title: v.string(),
    description: v.string(),
    category: v.string(),
    icon: v.string(),

    // Live session specific fields (optional)
    isLive: v.optional(v.boolean()),
    liveViewers: v.optional(v.number()),
    duration: v.optional(v.string()),

    // Metadata
    createdAt: v.number(),
    updatedAt: v.number(),

    // Additional fields for regular library items
    sources: v.optional(v.number()),
    author: v.optional(v.string()),
    publishedIn: v.optional(v.string()),
    dateCreated: v.optional(v.string()),
  })
    .index("by_category", ["category"])
    .index("by_isLive", ["isLive"])
    .index("by_createdAt", ["createdAt"]),

  sessions: defineTable({
    // Basic session information
    name: v.string(),
    description: v.optional(v.string()),
    content: v.string(), // Initial content/prompt from textarea

    // Session settings
    isPrivate: v.boolean(), // true = private, false = public
    allowQuestions: v.boolean(), // Allow others to join and ask questions
    timeLimit: v.number(), // Time limit in minutes (default 30)

    // Tutor settings
    tutorLanguage: v.string(), // Selected language for the tutor
    tutorGender: v.string(), // Selected gender: "male" | "female" | "neutral"

    // Resources
    resources: v.optional(v.array(v.object({
      id: v.string(),
      name: v.string(),
      type: v.string(), // "document" | "image" | "link"
      url: v.optional(v.string()), // For uploaded files or links
    }))),

    // Pre-defined questions
    questions: v.optional(v.array(v.string())),

    // Session status and metadata
    status: v.string(), // "draft" | "active" | "completed" | "expired"
    createdBy: v.optional(v.string()), // User ID who created the session
    createdAt: v.number(),
    updatedAt: v.number(),
    startedAt: v.optional(v.number()),
    endedAt: v.optional(v.number()),

    // Participants and interactions
    participantCount: v.optional(v.number()),
    messageCount: v.optional(v.number()),
  })
    .index("by_status", ["status"])
    .index("by_createdBy", ["createdBy"])
    .index("by_isPrivate", ["isPrivate"])
    .index("by_createdAt", ["createdAt"]),
});
