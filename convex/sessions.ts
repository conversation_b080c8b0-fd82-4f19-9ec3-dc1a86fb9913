import { mutation, query } from "./_generated/server";
import { v } from "convex/values";

// Create a new session
export const createSession = mutation({
  args: {
    name: v.string(),
    description: v.optional(v.string()),
    content: v.string(),
    isPrivate: v.boolean(),
    allowQuestions: v.boolean(),
    timeLimit: v.number(),
    tutorLanguage: v.string(),
    tutorGender: v.string(),
    resources: v.optional(v.array(v.object({
      id: v.string(),
      name: v.string(),
      type: v.string(),
      url: v.optional(v.string()),
    }))),
    questions: v.optional(v.array(v.string())),
    createdBy: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const now = Date.now();
    
    const sessionId = await ctx.db.insert("sessions", {
      name: args.name,
      description: args.description,
      content: args.content,
      isPrivate: args.isPrivate,
      allowQuestions: args.allowQuestions,
      timeLimit: args.timeLimit,
      tutorLanguage: args.tutorLanguage,
      tutorGender: args.tutorGender,
      resources: args.resources,
      questions: args.questions,
      status: "draft",
      createdBy: args.createdBy,
      createdAt: now,
      updatedAt: now,
      participantCount: 0,
      messageCount: 0,
    });

    return sessionId;
  },
});

// Start a session (change status from draft to active)
export const startSession = mutation({
  args: {
    sessionId: v.id("sessions"),
  },
  handler: async (ctx, args) => {
    const now = Date.now();
    
    await ctx.db.patch(args.sessionId, {
      status: "active",
      startedAt: now,
      updatedAt: now,
    });

    return { success: true };
  },
});

// Get all sessions
export const getAllSessions = query({
  handler: async (ctx) => {
    return await ctx.db
      .query("sessions")
      .order("desc")
      .collect();
  },
});

// Get public sessions only
export const getPublicSessions = query({
  handler: async (ctx) => {
    return await ctx.db
      .query("sessions")
      .withIndex("by_isPrivate", (q) => q.eq("isPrivate", false))
      .order("desc")
      .collect();
  },
});

// Get sessions by user
export const getUserSessions = query({
  args: {
    userId: v.string(),
  },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("sessions")
      .withIndex("by_createdBy", (q) => q.eq("createdBy", args.userId))
      .order("desc")
      .collect();
  },
});

// Get active sessions
export const getActiveSessions = query({
  handler: async (ctx) => {
    return await ctx.db
      .query("sessions")
      .withIndex("by_status", (q) => q.eq("status", "active"))
      .order("desc")
      .collect();
  },
});

// Get session by ID
export const getSessionById = query({
  args: {
    sessionId: v.id("sessions"),
  },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.sessionId);
  },
});

// Update session participant count
export const updateParticipantCount = mutation({
  args: {
    sessionId: v.id("sessions"),
    count: v.number(),
  },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.sessionId, {
      participantCount: args.count,
      updatedAt: Date.now(),
    });
  },
});

// End a session
export const endSession = mutation({
  args: {
    sessionId: v.id("sessions"),
  },
  handler: async (ctx, args) => {
    const now = Date.now();
    
    await ctx.db.patch(args.sessionId, {
      status: "completed",
      endedAt: now,
      updatedAt: now,
    });

    return { success: true };
  },
});

// Delete a session
export const deleteSession = mutation({
  args: {
    sessionId: v.id("sessions"),
  },
  handler: async (ctx, args) => {
    await ctx.db.delete(args.sessionId);
    return { success: true };
  },
});

// Get session statistics
export const getSessionStats = query({
  handler: async (ctx) => {
    const allSessions = await ctx.db.query("sessions").collect();
    
    const stats = {
      total: allSessions.length,
      active: allSessions.filter(s => s.status === "active").length,
      completed: allSessions.filter(s => s.status === "completed").length,
      public: allSessions.filter(s => !s.isPrivate).length,
      private: allSessions.filter(s => s.isPrivate).length,
    };

    return stats;
  },
});
