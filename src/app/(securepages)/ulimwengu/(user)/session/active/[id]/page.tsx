'use client';

import React, { useState, useRef, useEffect } from 'react';
import { Box, Flex, Text, Heading, Avatar, TextField, IconButton } from '@radix-ui/themes';
import { PaperPlaneIcon, DotsVerticalIcon } from '@radix-ui/react-icons';

export default function ActiveSessionsPage() {
//   const [messages, setMessages] = useState(initialMessages);
//   const [input, setInput] = useState('');
//   const [selectedScenario, setSelectedScenario] = useState(0);
//   const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);


  return (
   <></>
  );
}