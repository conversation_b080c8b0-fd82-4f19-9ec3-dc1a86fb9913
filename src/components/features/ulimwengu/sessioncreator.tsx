/* eslint-disable @next/next/no-img-element */
import React, { useState } from "react";
import { Box } from "@radix-ui/themes";
import { useMutation } from "convex/react";
// import { api } from "../../../../convex/_generated/api";
import {
  Pencil1Icon,
  UploadIcon,
  ImageIcon,
  Link2Icon,
  ArrowRightIcon,
  CodeIcon,
  Cross2Icon,
  FileTextIcon,
  TrashIcon,
  CheckIcon,
  LockClosedIcon,
  GlobeIcon,
  PersonIcon,
  ChatBubbleIcon,
  ClockIcon,
} from "@radix-ui/react-icons";
import { api } from "../../../../convex/_generated/api";

// --- Helper for action buttons ---
const actionButtons = [
  { id: "write", label: "Écrire", icon: <Pencil1Icon />, color: "#D37C67" },
  { id: "upload", label: "Télécharger", icon: <UploadIcon />, color: "#D37C67" },
  { id: "image", label: "Image", icon: <ImageIcon />, color: "#D37C67" },
  { id: "link", label: "Lien", icon: <Link2Icon />, color: "#D37C67" },
];

// --- Component Props Interface ---
export interface CreatorBoxProps {
  style?: React.CSSProperties;
  onStartSession?: (content: string) => void;
  placeholder?: string;
}

// --- The CreatorBox Component ---
export const CreatorBox = (props: CreatorBoxProps) => {
  const {
    style,
    onStartSession,
    placeholder = "Commencer un nouvel explicateur...",
  } = props;

  // --- State Management ---
  // To store the text from the textarea
  const [textValue, setTextValue] = useState("");
  // To change border color on focus for better UX
  const [isFocused, setIsFocused] = useState(false);
  // To track which action is currently active
  const [activeAction, setActiveAction] = useState("write");
  // To show/hide upload popup
  const [showUploadPopup, setShowUploadPopup] = useState(false);
  // To show/hide session creation popup
  const [showSessionPopup, setShowSessionPopup] = useState(false);
  // To show/hide link input
  const [showLinkInput, setShowLinkInput] = useState(false);
  // Session creation form data
  const [sessionData, setSessionData] = useState({
    name: '',
    description: '',
    isPrivate: true, // Default to private
    allowQuestions: true, // Default to allow questions
    timeLimit: 30, // Default 30 minutes
    tutorLanguage: 'français', // Default language
    tutorGender: 'female', // Default gender
    questions: [] as string[],
    resources: [] as Array<{id: string, name: string, type: string, file?: File}>
  });

  // Content items (links, images, uploads) to display above textarea
  const [contentItems, setContentItems] = useState<Array<{
    id: string;
    type: 'link' | 'image' | 'document' | 'code';
    content: string;
    name?: string;
    file?: File;
    preview?: string;
  }>>([]);

  // Link input state
  const [linkInput, setLinkInput] = useState('');

  // Available languages and genders
  const availableLanguages = [
    { value: 'français', label: 'Français' },
    { value: 'english', label: 'English' },
    { value: 'español', label: 'Español' },
    { value: 'deutsch', label: 'Deutsch' },
    { value: 'italiano', label: 'Italiano' },
    { value: 'português', label: 'Português' },
  ];

  const availableGenders = [
    { value: 'female', label: 'Féminin', icon: '👩' },
    { value: 'male', label: 'Masculin', icon: '👨' },
    { value: 'neutral', label: 'Neutre', icon: '🤖' },
  ];

  // Convex mutation
  const createSessionMutation = useMutation(api.sessions.createSession);

  // --- Derived State ---
  // Check if there is any content to enable the button
  const hasContent = textValue.trim().length > 0;

  // --- Event Handlers ---
  const handleStartSession = () => {
    if (hasContent) {
      // Pre-populate session data with textarea content
      setSessionData(prev => ({
        ...prev,
        name: prev.name || `Session ${new Date().toLocaleDateString('fr-FR')}`,
        questions: prev.questions.length === 0 && textValue.trim()
          ? [textValue.trim()]
          : prev.questions
      }));
      setShowSessionPopup(true);
    }
  };

  const handleCreateSession = async () => {
    if (sessionData.name.trim()) {
      try {
        // Combine content items with session resources
        const allResources = [
          ...sessionData.resources.map(r => ({
            id: r.id,
            name: r.name,
            type: r.type,
            url: r.file ? URL.createObjectURL(r.file) : undefined
          })),
          ...contentItems.map(item => ({
            id: item.id,
            name: item.name || item.content,
            type: item.type,
            url: item.type === 'link' ? item.content :
                 item.file ? URL.createObjectURL(item.file) :
                 item.preview || undefined
          }))
        ];

        // Prepare session data for Convex
        const sessionPayload = {
          name: sessionData.name,
          description: sessionData.description || undefined,
          content: textValue,
          isPrivate: sessionData.isPrivate,
          allowQuestions: sessionData.allowQuestions,
          timeLimit: sessionData.timeLimit,
          tutorLanguage: sessionData.tutorLanguage,
          tutorGender: sessionData.tutorGender,
          questions: sessionData.questions.length > 0 ? sessionData.questions : undefined,
          resources: allResources.length > 0 ? allResources : undefined,
          createdBy: "current-user-id", // TODO: Replace with actual user ID
        };

        // Create session in Convex
        const sessionId = await createSessionMutation(sessionPayload);

        // Call the onStartSession callback if provided
        if (onStartSession) {
          onStartSession(sessionId);
        }

        // Reset form
        setSessionData({
          name: '',
          description: '',
          isPrivate: true,
          allowQuestions: true,
          timeLimit: 30,
          tutorLanguage: 'français',
          tutorGender: 'female',
          questions: [],
          resources: []
        });
        setTextValue('');
        setContentItems([]);
        setShowSessionPopup(false);

        console.log('Session created successfully:', sessionId);
      } catch (error) {
        console.error('Error creating session:', error);
      }
    }
  };

  const handleAddResource = (file: File, type: string) => {
    const newResource = {
      id: Date.now().toString(),
      name: file.name,
      type: type,
      file: file
    };
    setSessionData(prev => ({
      ...prev,
      resources: [...prev.resources, newResource]
    }));
  };

  const handleRemoveResource = (resourceId: string) => {
    setSessionData(prev => ({
      ...prev,
      resources: prev.resources.filter(r => r.id !== resourceId)
    }));
  };

  const handleAddQuestion = (question: string) => {
    if (question.trim()) {
      setSessionData(prev => ({
        ...prev,
        questions: [...prev.questions, question.trim()]
      }));
    }
  };

  const handleRemoveQuestion = (index: number) => {
    setSessionData(prev => ({
      ...prev,
      questions: prev.questions.filter((_, i) => i !== index)
    }));
  };

  const handleActionClick = (actionId: string) => {
    setActiveAction(actionId);

    switch (actionId) {
      case "upload":
        setShowUploadPopup(true);
        break;
      case "image":
        // Trigger image upload
        const imageInput = document.createElement('input');
        imageInput.type = 'file';
        imageInput.accept = 'image/*';
        imageInput.onchange = (e) => {
          const file = (e.target as HTMLInputElement).files?.[0];
          if (file) {
            handleAddContentItem('image', file.name, file);
          }
        };
        imageInput.click();
        break;
      case "link":
        // Show link input in a more elegant way
        setShowLinkInput(true);
        break;
      case "write":
        // Default action - just set as active
        break;
    }
  };

  const handleUploadOption = (option: string) => {
    console.log(`Option sélectionnée: ${option}`);
    setShowUploadPopup(false);

    switch (option) {
      case 'file':
        // Trigger file upload
        const fileInput = document.createElement('input');
        fileInput.type = 'file';
        fileInput.accept = '.pdf,.doc,.docx,.txt,.md';
        fileInput.onchange = (e) => {
          const file = (e.target as HTMLInputElement).files?.[0];
          if (file) {
            handleAddContentItem('document', file.name, file);
          }
        };
        fileInput.click();
        break;
      case 'google-drive':
        // TODO: Implement Google Drive integration
        alert('Intégration Google Drive à venir...');
        break;
      case 'import-code':
        // Show code input prompt
        const code = prompt('Collez votre code ici:');
        if (code && code.trim()) {
          handleAddContentItem('code', 'Code snippet', undefined, code.trim());
        }
        break;
    }
  };

  const handleAddContentItem = (type: 'link' | 'image' | 'document' | 'code', content: string, file?: File, preview?: string) => {
    const newItem = {
      id: Date.now().toString(),
      type,
      content,
      name: file?.name || content,
      file,
      preview: preview || (file && type === 'image' ? URL.createObjectURL(file) : undefined)
    };

    setContentItems(prev => [...prev, newItem]);
  };

  const handleRemoveContentItem = (itemId: string) => {
    setContentItems(prev => prev.filter(item => item.id !== itemId));
  };

  // --- Component Styles ---
  // Beautiful glassy design with gradient and glassmorphism
  const containerStyle: React.CSSProperties = {
    background: isFocused
      ? "linear-gradient(135deg, rgba(57, 39, 53, 0.95) 0%, rgba(211, 124, 103, 0.15) 100%)"
      : "linear-gradient(135deg, rgba(57, 39, 53, 0.85) 0%, rgba(57, 39, 53, 0.65) 100%)",
    backdropFilter: "blur(20px)",
    WebkitBackdropFilter: "blur(20px)",
    borderRadius: "24px",
    border: isFocused
      ? "1px solid rgba(211, 124, 103, 0.4)"
      : "1px solid rgba(255, 255, 255, 0.1)",
    padding: "32px",
    marginBottom: "20px",
    boxShadow: isFocused
      ? "0 20px 40px rgba(211, 124, 103, 0.2), 0 8px 32px rgba(0, 0, 0, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.1)"
      : "0 16px 32px rgba(0, 0, 0, 0.2), 0 8px 16px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.05)",
    transition: "all 0.4s cubic-bezier(0.4, 0, 0.2, 1)",
    width: '100%',
    position: 'relative',
    overflow: 'hidden',
    ...style,
  };

  const textareaStyle: React.CSSProperties = {
    width: "100%",
    minHeight: "60px",
    background: "rgba(255, 255, 255, 0.05)",
    backdropFilter: "blur(10px)",
    WebkitBackdropFilter: "blur(10px)",
    border: "1px solid rgba(255, 255, 255, 0.1)",
    borderRadius: "16px",
    outline: "none",
    resize: "none",
    fontSize: "18px",
    fontWeight: 500,
    color: "#ffffff",
    marginBottom: "24px",
    padding: "16px 20px",
    transition: "all 0.3s ease",
    fontFamily: "inherit",
    lineHeight: "1.5",
  };

  const actionsContainerStyle: React.CSSProperties = {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
    flexWrap: 'wrap',
    gap: '20px',
    marginTop: "8px",
  };

  const actionButtonStyle: React.CSSProperties = {
    display: "flex",
    alignItems: "center",
    gap: "10px",
    background: "rgba(255, 255, 255, 0.08)",
    backdropFilter: "blur(10px)",
    WebkitBackdropFilter: "blur(10px)",
    border: "1px solid rgba(255, 255, 255, 0.1)",
    borderRadius: "12px",
    cursor: "pointer",
    color: "#ffffff",
    fontSize: "14px",
    fontWeight: 500,
    padding: "12px 16px",
    transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
    position: 'relative',
    overflow: 'hidden',
  };

  return (
    <>
      {/* Custom CSS for placeholder styling and animations */}
      <style jsx>{`
        textarea::placeholder {
          color: rgba(255, 255, 255, 0.5);
          font-style: italic;
          font-weight: 400;
        }
        textarea:focus::placeholder {
          color: rgba(211, 124, 103, 0.7);
        }
        @keyframes fadeIn {
          from {
            opacity: 0;
            transform: scale(0.9);
          }
          to {
            opacity: 1;
            transform: scale(1);
          }
        }
      `}</style>

      <Box style={containerStyle}>
        {/* Decorative gradient overlay */}
        <div style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          height: '2px',
          background: 'linear-gradient(90deg, transparent 0%, #D37C67 50%, transparent 100%)',
          opacity: isFocused ? 1 : 0.3,
          transition: 'opacity 0.3s ease',
        }} />

        {/* Content Items Display */}
        {contentItems.length > 0 && (
          <div style={{
            marginBottom: '20px',
            display: 'flex',
            flexDirection: 'column',
            gap: '12px',
          }}>
            {contentItems.map((item) => (
              <div
                key={item.id}
                style={{
                  background: 'rgba(255, 255, 255, 0.1)',
                  backdropFilter: 'blur(10px)',
                  WebkitBackdropFilter: 'blur(10px)',
                  borderRadius: '16px',
                  border: '1px solid rgba(255, 255, 255, 0.2)',
                  padding: '16px',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '12px',
                  position: 'relative',
                  transition: 'all 0.3s ease',
                }}
              >
                {/* Content Icon */}
                <div style={{
                  width: '40px',
                  height: '40px',
                  borderRadius: '12px',
                  background: 'rgba(211, 124, 103, 0.2)',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  color: '#D37C67',
                  fontSize: '18px',
                  flexShrink: 0,
                }}>
                  {item.type === 'link' && <Link2Icon />}
                  {item.type === 'image' && <ImageIcon />}
                  {item.type === 'document' && <FileTextIcon />}
                  {item.type === 'code' && <CodeIcon />}
                </div>

                {/* Content Preview */}
                <div style={{ flex: 1, minWidth: 0 }}>
                  {item.type === 'image' && item.preview ? (
                    <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                      <img
                        src={item.preview}
                        alt={item.name}
                        style={{
                          width: '60px',
                          height: '60px',
                          objectFit: 'cover',
                          borderRadius: '8px',
                          border: '1px solid rgba(255, 255, 255, 0.2)',
                        }}
                      />
                      <div>
                        <div style={{
                          color: '#ffffff',
                          fontSize: '14px',
                          fontWeight: 600,
                          marginBottom: '4px',
                        }}>
                          {item.name}
                        </div>
                        <div style={{
                          color: 'rgba(255, 255, 255, 0.7)',
                          fontSize: '12px',
                        }}>
                          Image • {item.file ? Math.round(item.file.size / 1024) + ' KB' : ''}
                        </div>
                      </div>
                    </div>
                  ) : item.type === 'link' ? (
                    <div>
                      <div style={{
                        color: '#ffffff',
                        fontSize: '14px',
                        fontWeight: 600,
                        marginBottom: '4px',
                      }}>
                        Lien ajouté
                      </div>
                      <a
                        href={item.content}
                        target="_blank"
                        rel="noopener noreferrer"
                        style={{
                          color: '#D37C67',
                          fontSize: '13px',
                          textDecoration: 'none',
                          wordBreak: 'break-all',
                        }}
                      >
                        {item.content}
                      </a>
                    </div>
                  ) : item.type === 'code' ? (
                    <div>
                      <div style={{
                        color: '#ffffff',
                        fontSize: '14px',
                        fontWeight: 600,
                        marginBottom: '4px',
                      }}>
                        Code snippet
                      </div>
                      <div style={{
                        color: 'rgba(255, 255, 255, 0.8)',
                        fontSize: '12px',
                        fontFamily: 'monospace',
                        background: 'rgba(0, 0, 0, 0.2)',
                        padding: '8px',
                        borderRadius: '6px',
                        maxHeight: '60px',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                      }}>
                        {item.preview?.substring(0, 100)}...
                      </div>
                    </div>
                  ) : (
                    <div>
                      <div style={{
                        color: '#ffffff',
                        fontSize: '14px',
                        fontWeight: 600,
                        marginBottom: '4px',
                      }}>
                        {item.name}
                      </div>
                      <div style={{
                        color: 'rgba(255, 255, 255, 0.7)',
                        fontSize: '12px',
                        textTransform: 'capitalize',
                      }}>
                        {item.type} • {item.file ? Math.round(item.file.size / 1024) + ' KB' : ''}
                      </div>
                    </div>
                  )}
                </div>

                {/* Remove Button */}
                <button
                  onClick={() => handleRemoveContentItem(item.id)}
                  style={{
                    background: 'rgba(255, 255, 255, 0.1)',
                    border: '1px solid rgba(255, 255, 255, 0.2)',
                    borderRadius: '8px',
                    color: '#ffffff',
                    cursor: 'pointer',
                    padding: '8px',
                    transition: 'all 0.3s ease',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    flexShrink: 0,
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.background = 'rgba(255, 0, 0, 0.2)';
                    e.currentTarget.style.borderColor = 'rgba(255, 0, 0, 0.4)';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.background = 'rgba(255, 255, 255, 0.1)';
                    e.currentTarget.style.borderColor = 'rgba(255, 255, 255, 0.2)';
                  }}
                >
                  <Cross2Icon width="14" height="14" />
                </button>
              </div>
            ))}
          </div>
        )}

      <textarea
        style={{
          ...textareaStyle,
          background: isFocused
            ? "rgba(255, 255, 255, 0.1)"
            : "rgba(255, 255, 255, 0.05)",
          borderColor: isFocused
            ? "rgba(211, 124, 103, 0.3)"
            : "rgba(255, 255, 255, 0.1)",
          boxShadow: isFocused
            ? "0 8px 32px rgba(211, 124, 103, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.1)"
            : "0 4px 16px rgba(0, 0, 0, 0.1)",
          transform: isFocused ? 'scale(1.01)' : 'scale(1)',
        }}
        placeholder={placeholder}
        value={textValue}
        onChange={(e) => setTextValue(e.target.value)}
        onFocus={() => setIsFocused(true)}
        onBlur={() => setIsFocused(false)}
        rows={3}
      />

      <div style={actionsContainerStyle}>
        {/* Left side: Action Buttons */}
        <div style={{ display: "flex", alignItems: "center", gap: "12px", flexWrap: 'wrap' }}>
          {actionButtons.map((action) => {
            const isActive = activeAction === action.id;
            // Count content items for this action type
            const getContentCount = () => {
              switch (action.id) {
                case 'link':
                  return contentItems.filter(item => item.type === 'link').length;
                case 'image':
                  return contentItems.filter(item => item.type === 'image').length;
                case 'upload':
                  return contentItems.filter(item => item.type === 'document').length;
                default:
                  return 0;
              }
            };
            const contentCount = getContentCount();

            return (
              <button
                key={action.id}
                title={action.label}
                style={{
                  ...actionButtonStyle,
                  background: isActive
                    ? 'rgba(211, 124, 103, 0.3)'
                    : contentCount > 0
                    ? 'rgba(211, 124, 103, 0.15)'
                    : 'rgba(255, 255, 255, 0.08)',
                  borderColor: isActive
                    ? 'rgba(211, 124, 103, 0.5)'
                    : contentCount > 0
                    ? 'rgba(211, 124, 103, 0.3)'
                    : 'rgba(255, 255, 255, 0.1)',
                  boxShadow: isActive
                    ? '0 4px 20px rgba(211, 124, 103, 0.3)'
                    : contentCount > 0
                    ? '0 2px 10px rgba(211, 124, 103, 0.15)'
                    : 'none',
                  position: 'relative',
                }}
                onClick={() => handleActionClick(action.id)}
                onMouseEnter={(e) => {
                  if (!isActive) {
                    e.currentTarget.style.background = 'rgba(211, 124, 103, 0.2)';
                    e.currentTarget.style.borderColor = 'rgba(211, 124, 103, 0.4)';
                    e.currentTarget.style.transform = 'translateY(-2px)';
                    e.currentTarget.style.boxShadow = '0 8px 25px rgba(211, 124, 103, 0.2)';
                  }
                }}
                onMouseLeave={(e) => {
                  if (!isActive) {
                    e.currentTarget.style.background = contentCount > 0 ? 'rgba(211, 124, 103, 0.15)' : 'rgba(255, 255, 255, 0.08)';
                    e.currentTarget.style.borderColor = contentCount > 0 ? 'rgba(211, 124, 103, 0.3)' : 'rgba(255, 255, 255, 0.1)';
                    e.currentTarget.style.transform = 'translateY(0)';
                    e.currentTarget.style.boxShadow = contentCount > 0 ? '0 2px 10px rgba(211, 124, 103, 0.15)' : 'none';
                  }
                }}
              >
                <span style={{ color: action.color, fontSize: '16px' }}>{action.icon}</span>
                <span style={{ fontSize: '13px', fontWeight: 600 }}>{action.label}</span>

                {/* Content Count Badge */}
                {contentCount > 0 && (
                  <span style={{
                    position: 'absolute',
                    top: '-6px',
                    right: '-6px',
                    background: 'linear-gradient(135deg, #D37C67 0%, #E8967A 100%)',
                    color: '#ffffff',
                    fontSize: '10px',
                    fontWeight: 700,
                    borderRadius: '50%',
                    width: '18px',
                    height: '18px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    border: '2px solid rgba(57, 39, 53, 0.8)',
                    boxShadow: '0 2px 8px rgba(211, 124, 103, 0.4)',
                  }}>
                    {contentCount}
                  </span>
                )}
              </button>
            );
          })}
        </div>

        {/* Right side: Conditional "Start Session" Button */}
        {hasContent && (
          <button
            style={{
              background: 'linear-gradient(135deg, #D37C67 0%, #E8967A 100%)',
              border: 'none',
              borderRadius: '16px',
              color: '#ffffff',
              fontSize: '16px',
              fontWeight: 700,
              padding: '16px 32px',
              cursor: 'pointer',
              transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
              boxShadow: '0 8px 25px rgba(211, 124, 103, 0.3)',
              display: 'flex',
              alignItems: 'center',
              gap: '8px',
              position: 'relative',
              overflow: 'hidden',
            }}
            onClick={handleStartSession}
            onMouseEnter={(e) => {
              e.currentTarget.style.transform = 'translateY(-3px) scale(1.02)';
              e.currentTarget.style.boxShadow = '0 12px 35px rgba(211, 124, 103, 0.4)';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.transform = 'translateY(0) scale(1)';
              e.currentTarget.style.boxShadow = '0 8px 25px rgba(211, 124, 103, 0.3)';
            }}
          >
            <span>Commencer la Session</span>
            <ArrowRightIcon style={{ fontSize: '18px' }} />
          </button>
        )}
      </div>

      {/* Upload Options Popup */}
     
    </Box>
    {showUploadPopup && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'rgba(0, 0, 0, 0.7)',
          backdropFilter: 'blur(10px)',
          WebkitBackdropFilter: 'blur(10px)',
          zIndex: 1000,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          animation: 'fadeIn 0.3s ease',
        }}>
          <div style={{
            background: 'linear-gradient(135deg, rgba(57, 39, 53, 0.95) 0%, rgba(57, 39, 53, 0.85) 100%)',
            backdropFilter: 'blur(20px)',
            WebkitBackdropFilter: 'blur(20px)',
            borderRadius: '24px',
            border: '1px solid rgba(255, 255, 255, 0.1)',
            padding: '32px',
            minWidth: '400px',
            boxShadow: '0 20px 40px rgba(0, 0, 0, 0.3)',
            position: 'relative',
          }}>
            {/* Close button */}
            <button
              style={{
                position: 'absolute',
                top: '16px',
                right: '16px',
                background: 'rgba(255, 255, 255, 0.1)',
                border: '1px solid rgba(255, 255, 255, 0.2)',
                borderRadius: '8px',
                color: '#ffffff',
                cursor: 'pointer',
                padding: '8px',
                transition: 'all 0.2s ease',
              }}
              onClick={() => setShowUploadPopup(false)}
              onMouseEnter={(e) => {
                e.currentTarget.style.background = 'rgba(211, 124, 103, 0.3)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.background = 'rgba(255, 255, 255, 0.1)';
              }}
            >
              <Cross2Icon />
            </button>

            <h3 style={{
              color: '#ffffff',
              fontSize: '24px',
              fontWeight: 700,
              marginBottom: '24px',
              textAlign: 'center',
            }}>
              Options de Téléchargement
            </h3>

            <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
              {[
                { id: 'file', label: 'Télécharger un fichier', icon: <UploadIcon /> },
                { id: 'google-drive', label: 'Depuis Google Drive', icon: <Link2Icon /> },
                { id: 'import-code', label: 'Importer du code', icon: <CodeIcon /> },
              ].map((option) => (
                <button
                  key={option.id}
                  style={{
                    background: 'rgba(255, 255, 255, 0.08)',
                    backdropFilter: 'blur(10px)',
                    WebkitBackdropFilter: 'blur(10px)',
                    border: '1px solid rgba(255, 255, 255, 0.1)',
                    borderRadius: '16px',
                    color: '#ffffff',
                    fontSize: '16px',
                    fontWeight: 600,
                    padding: '16px 24px',
                    cursor: 'pointer',
                    transition: 'all 0.3s ease',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '12px',
                    width: '100%',
                  }}
                  onClick={() => handleUploadOption(option.id)}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.background = 'rgba(211, 124, 103, 0.2)';
                    e.currentTarget.style.borderColor = 'rgba(211, 124, 103, 0.4)';
                    e.currentTarget.style.transform = 'translateY(-2px)';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.background = 'rgba(255, 255, 255, 0.08)';
                    e.currentTarget.style.borderColor = 'rgba(255, 255, 255, 0.1)';
                    e.currentTarget.style.transform = 'translateY(0)';
                  }}
                >
                  <span style={{ color: '#D37C67', fontSize: '20px' }}>{option.icon}</span>
                  <span>{option.label}</span>
                </button>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Link Input Popup */}
      {showLinkInput && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'rgba(0, 0, 0, 0.7)',
          backdropFilter: 'blur(10px)',
          WebkitBackdropFilter: 'blur(10px)',
          zIndex: 1000,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          animation: 'fadeIn 0.3s ease',
        }}>
          <div style={{
            background: 'linear-gradient(135deg, rgba(57, 39, 53, 0.98) 0%, rgba(57, 39, 53, 0.95) 100%)',
            backdropFilter: 'blur(25px)',
            WebkitBackdropFilter: 'blur(25px)',
            borderRadius: '24px',
            border: '1px solid rgba(211, 124, 103, 0.3)',
            padding: '32px',
            minWidth: '400px',
            maxWidth: '500px',
            boxShadow: '0 25px 50px rgba(0, 0, 0, 0.4), 0 0 0 1px rgba(255, 255, 255, 0.05)',
            position: 'relative',
          }}>
            {/* Decorative gradient overlay */}
            <div style={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              height: '4px',
              background: 'linear-gradient(90deg, transparent 0%, #D37C67 50%, transparent 100%)',
              borderRadius: '24px 24px 0 0',
            }} />

            {/* Close button */}
            <button
              style={{
                position: 'absolute',
                top: '16px',
                right: '16px',
                background: 'rgba(255, 255, 255, 0.1)',
                border: '1px solid rgba(255, 255, 255, 0.2)',
                borderRadius: '8px',
                color: '#ffffff',
                cursor: 'pointer',
                padding: '8px',
                transition: 'all 0.3s ease',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
              onClick={() => {
                setShowLinkInput(false);
                setLinkInput('');
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.background = 'rgba(211, 124, 103, 0.3)';
                e.currentTarget.style.transform = 'scale(1.1)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.background = 'rgba(255, 255, 255, 0.1)';
                e.currentTarget.style.transform = 'scale(1)';
              }}
            >
              <Cross2Icon width="16" height="16" />
            </button>

            {/* Header */}
            <div style={{ marginBottom: '24px', textAlign: 'center' }}>
              <h3 style={{
                color: '#ffffff',
                fontSize: '24px',
                fontWeight: 700,
                marginBottom: '8px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                gap: '8px',
              }}>
                <Link2Icon width="24" height="24" style={{ color: '#D37C67' }} />
                Ajouter un Lien
              </h3>
              <p style={{
                color: 'rgba(255, 255, 255, 0.7)',
                fontSize: '14px',
                margin: 0,
                lineHeight: 1.5,
              }}>
                Entrez l&apos;URL que vous souhaitez ajouter à votre session
              </p>
            </div>

            {/* Link Input */}
            <div style={{ marginBottom: '24px' }}>
              <input
                type="url"
                placeholder="https://example.com"
                value={linkInput}
                onChange={(e) => setLinkInput(e.target.value)}
                autoFocus
                style={{
                  width: '100%',
                  background: 'rgba(255, 255, 255, 0.08)',
                  backdropFilter: 'blur(10px)',
                  WebkitBackdropFilter: 'blur(10px)',
                  border: '2px solid rgba(255, 255, 255, 0.1)',
                  borderRadius: '16px',
                  color: '#ffffff',
                  fontSize: '16px',
                  fontWeight: 500,
                  padding: '16px 20px',
                  outline: 'none',
                  transition: 'all 0.3s ease',
                  fontFamily: 'inherit',
                }}
                onFocus={(e) => {
                  e.currentTarget.style.borderColor = 'rgba(211, 124, 103, 0.5)';
                  e.currentTarget.style.background = 'rgba(255, 255, 255, 0.12)';
                  e.currentTarget.style.boxShadow = '0 0 0 4px rgba(211, 124, 103, 0.1)';
                }}
                onBlur={(e) => {
                  e.currentTarget.style.borderColor = 'rgba(255, 255, 255, 0.1)';
                  e.currentTarget.style.background = 'rgba(255, 255, 255, 0.08)';
                  e.currentTarget.style.boxShadow = 'none';
                }}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' && linkInput.trim()) {
                    handleAddContentItem('link', linkInput.trim());
                    setLinkInput('');
                    setShowLinkInput(false);
                  }
                }}
              />
            </div>

            {/* Action Buttons */}
            <div style={{
              display: 'flex',
              gap: '12px',
              justifyContent: 'flex-end',
            }}>
              {/* Cancel Button */}
              <button
                onClick={() => {
                  setShowLinkInput(false);
                  setLinkInput('');
                }}
                style={{
                  background: 'rgba(255, 255, 255, 0.1)',
                  border: '2px solid rgba(255, 255, 255, 0.2)',
                  borderRadius: '12px',
                  color: '#ffffff',
                  fontSize: '14px',
                  fontWeight: 600,
                  padding: '12px 20px',
                  cursor: 'pointer',
                  transition: 'all 0.3s ease',
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.background = 'rgba(255, 255, 255, 0.15)';
                  e.currentTarget.style.transform = 'translateY(-2px)';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.background = 'rgba(255, 255, 255, 0.1)';
                  e.currentTarget.style.transform = 'translateY(0)';
                }}
              >
                Annuler
              </button>

              {/* Add Button */}
              <button
                onClick={() => {
                  if (linkInput.trim()) {
                    handleAddContentItem('link', linkInput.trim());
                    setLinkInput('');
                    setShowLinkInput(false);
                  }
                }}
                disabled={!linkInput.trim()}
                style={{
                  background: linkInput.trim()
                    ? 'linear-gradient(135deg, #D37C67 0%, #E8967A 100%)'
                    : 'rgba(255, 255, 255, 0.1)',
                  border: 'none',
                  borderRadius: '12px',
                  color: linkInput.trim() ? '#ffffff' : 'rgba(255, 255, 255, 0.5)',
                  fontSize: '14px',
                  fontWeight: 700,
                  padding: '12px 24px',
                  cursor: linkInput.trim() ? 'pointer' : 'not-allowed',
                  transition: 'all 0.3s ease',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '6px',
                  boxShadow: linkInput.trim()
                    ? '0 8px 25px rgba(211, 124, 103, 0.3)'
                    : 'none',
                }}
                onMouseEnter={(e) => {
                  if (linkInput.trim()) {
                    e.currentTarget.style.transform = 'translateY(-2px) scale(1.02)';
                    e.currentTarget.style.boxShadow = '0 12px 35px rgba(211, 124, 103, 0.4)';
                  }
                }}
                onMouseLeave={(e) => {
                  if (linkInput.trim()) {
                    e.currentTarget.style.transform = 'translateY(0) scale(1)';
                    e.currentTarget.style.boxShadow = '0 8px 25px rgba(211, 124, 103, 0.3)';
                  }
                }}
              >
                <Link2Icon width="16" height="16" />
                Ajouter le Lien
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Session Creation Popup */}
      {showSessionPopup && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'rgba(0, 0, 0, 0.8)',
          backdropFilter: 'blur(15px)',
          WebkitBackdropFilter: 'blur(15px)',
          zIndex: 1001,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          animation: 'fadeIn 0.3s ease',
        }}>
          <div style={{
            background: 'linear-gradient(135deg, rgba(57, 39, 53, 0.98) 0%, rgba(57, 39, 53, 0.95) 100%)',
            backdropFilter: 'blur(25px)',
            WebkitBackdropFilter: 'blur(25px)',
            borderRadius: '28px',
            border: '1px solid rgba(211, 124, 103, 0.3)',
            padding: '40px',
            minWidth: '500px',
            maxWidth: '600px',
            maxHeight: '80vh',
            overflowY: 'auto',
            boxShadow: '0 25px 50px rgba(0, 0, 0, 0.4), 0 0 0 1px rgba(255, 255, 255, 0.05)',
            position: 'relative',
          }}>
            {/* Decorative gradient overlay */}
            <div style={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              height: '4px',
              background: 'linear-gradient(90deg, transparent 0%, #D37C67 50%, transparent 100%)',
              borderRadius: '28px 28px 0 0',
            }} />

            {/* Close button */}
            <button
              style={{
                position: 'absolute',
                top: '20px',
                right: '20px',
                background: 'rgba(255, 255, 255, 0.1)',
                border: '1px solid rgba(255, 255, 255, 0.2)',
                borderRadius: '12px',
                color: '#ffffff',
                cursor: 'pointer',
                padding: '12px',
                transition: 'all 0.3s ease',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
              onClick={() => setShowSessionPopup(false)}
              onMouseEnter={(e) => {
                e.currentTarget.style.background = 'rgba(211, 124, 103, 0.3)';
                e.currentTarget.style.transform = 'scale(1.1)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.background = 'rgba(255, 255, 255, 0.1)';
                e.currentTarget.style.transform = 'scale(1)';
              }}
            >
              <Cross2Icon width="18" height="18" />
            </button>

            {/* Header */}
            <div style={{ marginBottom: '32px', textAlign: 'center' }}>
              <h2 style={{
                color: '#ffffff',
                fontSize: '28px',
                fontWeight: 800,
                marginBottom: '8px',
                background: 'linear-gradient(135deg, #ffffff 0%, #D37C67 100%)',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                backgroundClip: 'text',
              }}>
                Créer une Nouvelle Session
              </h2>
              <p style={{
                color: 'rgba(255, 255, 255, 0.7)',
                fontSize: '16px',
                margin: 0,
                lineHeight: 1.5,
              }}>
                Configurez votre session d&apos;apprentissage personnalisée
              </p>
            </div>

            {/* Session Name Input */}
            <div style={{ marginBottom: '24px' }}>
              <label style={{
                display: 'block',
                color: '#ffffff',
                fontSize: '16px',
                fontWeight: 600,
                marginBottom: '8px',
              }}>
                Nom de la Session *
              </label>
              <input
                type="text"
                placeholder="Ex: Entretien d'embauche avec Luna"
                value={sessionData.name}
                onChange={(e) => setSessionData(prev => ({ ...prev, name: e.target.value }))}
                style={{
                  width: '100%',
                  background: 'rgba(255, 255, 255, 0.08)',
                  backdropFilter: 'blur(10px)',
                  WebkitBackdropFilter: 'blur(10px)',
                  border: '2px solid rgba(255, 255, 255, 0.1)',
                  borderRadius: '16px',
                  color: '#ffffff',
                  fontSize: '16px',
                  fontWeight: 500,
                  padding: '16px 20px',
                  outline: 'none',
                  transition: 'all 0.3s ease',
                  fontFamily: 'inherit',
                }}
                onFocus={(e) => {
                  e.currentTarget.style.borderColor = 'rgba(211, 124, 103, 0.5)';
                  e.currentTarget.style.background = 'rgba(255, 255, 255, 0.12)';
                  e.currentTarget.style.boxShadow = '0 0 0 4px rgba(211, 124, 103, 0.1)';
                }}
                onBlur={(e) => {
                  e.currentTarget.style.borderColor = 'rgba(255, 255, 255, 0.1)';
                  e.currentTarget.style.background = 'rgba(255, 255, 255, 0.08)';
                  e.currentTarget.style.boxShadow = 'none';
                }}
              />
            </div>

            {/* Session Description Input */}
            <div style={{ marginBottom: '24px' }}>
              <label style={{
                display: 'block',
                color: '#ffffff',
                fontSize: '16px',
                fontWeight: 600,
                marginBottom: '8px',
              }}>
                Description (optionnel)
              </label>
              <textarea
                placeholder="Décrivez brièvement l'objectif de cette session..."
                value={sessionData.description}
                onChange={(e) => setSessionData(prev => ({ ...prev, description: e.target.value }))}
                rows={3}
                style={{
                  width: '100%',
                  background: 'rgba(255, 255, 255, 0.08)',
                  backdropFilter: 'blur(10px)',
                  WebkitBackdropFilter: 'blur(10px)',
                  border: '2px solid rgba(255, 255, 255, 0.1)',
                  borderRadius: '16px',
                  color: '#ffffff',
                  fontSize: '16px',
                  fontWeight: 500,
                  padding: '16px 20px',
                  outline: 'none',
                  resize: 'vertical',
                  minHeight: '80px',
                  transition: 'all 0.3s ease',
                  fontFamily: 'inherit',
                  lineHeight: 1.5,
                }}
                onFocus={(e) => {
                  e.currentTarget.style.borderColor = 'rgba(211, 124, 103, 0.5)';
                  e.currentTarget.style.background = 'rgba(255, 255, 255, 0.12)';
                  e.currentTarget.style.boxShadow = '0 0 0 4px rgba(211, 124, 103, 0.1)';
                }}
                onBlur={(e) => {
                  e.currentTarget.style.borderColor = 'rgba(255, 255, 255, 0.1)';
                  e.currentTarget.style.background = 'rgba(255, 255, 255, 0.08)';
                  e.currentTarget.style.boxShadow = 'none';
                }}
              />
            </div>

            {/* Session Settings Row */}
            <div style={{
              display: 'grid',
              gridTemplateColumns: '1fr 1fr',
              gap: '20px',
              marginBottom: '24px'
            }}>
              {/* Privacy Setting */}
              <div>
                <label style={{
                  display: 'block',
                  color: '#ffffff',
                  fontSize: '16px',
                  fontWeight: 600,
                  marginBottom: '12px',
                }}>
                  Confidentialité
                </label>
                <div style={{ display: 'flex', gap: '8px' }}>
                  <button
                    onClick={() => setSessionData(prev => ({ ...prev, isPrivate: true }))}
                    style={{
                      flex: 1,
                      background: sessionData.isPrivate
                        ? 'linear-gradient(135deg, rgba(211, 124, 103, 0.3) 0%, rgba(211, 124, 103, 0.1) 100%)'
                        : 'rgba(255, 255, 255, 0.08)',
                      border: sessionData.isPrivate
                        ? '2px solid rgba(211, 124, 103, 0.6)'
                        : '1px solid rgba(255, 255, 255, 0.1)',
                      borderRadius: '12px',
                      color: '#ffffff',
                      fontSize: '14px',
                      fontWeight: 600,
                      padding: '12px 16px',
                      cursor: 'pointer',
                      transition: 'all 0.3s ease',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      gap: '6px',
                    }}
                  >
                    <LockClosedIcon width="16" height="16" />
                    Privée
                  </button>
                  <button
                    onClick={() => setSessionData(prev => ({ ...prev, isPrivate: false }))}
                    style={{
                      flex: 1,
                      background: !sessionData.isPrivate
                        ? 'linear-gradient(135deg, rgba(211, 124, 103, 0.3) 0%, rgba(211, 124, 103, 0.1) 100%)'
                        : 'rgba(255, 255, 255, 0.08)',
                      border: !sessionData.isPrivate
                        ? '2px solid rgba(211, 124, 103, 0.6)'
                        : '1px solid rgba(255, 255, 255, 0.1)',
                      borderRadius: '12px',
                      color: '#ffffff',
                      fontSize: '14px',
                      fontWeight: 600,
                      padding: '12px 16px',
                      cursor: 'pointer',
                      transition: 'all 0.3s ease',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      gap: '6px',
                    }}
                  >
                    <GlobeIcon width="16" height="16" />
                    Publique
                  </button>
                </div>
              </div>

              {/* Time Limit */}
              <div>
                <label style={{
                  display: 'block',
                  color: '#ffffff',
                  fontSize: '16px',
                  fontWeight: 600,
                  marginBottom: '8px',
                }}>
                  Durée (minutes)
                </label>
                <input
                  type="number"
                  min="5"
                  max="120"
                  value={sessionData.timeLimit}
                  onChange={(e) => setSessionData(prev => ({ ...prev, timeLimit: parseInt(e.target.value) || 30 }))}
                  style={{
                    width: '100%',
                    background: 'rgba(255, 255, 255, 0.08)',
                    backdropFilter: 'blur(10px)',
                    WebkitBackdropFilter: 'blur(10px)',
                    border: '2px solid rgba(255, 255, 255, 0.1)',
                    borderRadius: '12px',
                    color: '#ffffff',
                    fontSize: '16px',
                    fontWeight: 500,
                    padding: '12px 16px',
                    outline: 'none',
                    transition: 'all 0.3s ease',
                    fontFamily: 'inherit',
                  }}
                  onFocus={(e) => {
                    e.currentTarget.style.borderColor = 'rgba(211, 124, 103, 0.5)';
                    e.currentTarget.style.background = 'rgba(255, 255, 255, 0.12)';
                    e.currentTarget.style.boxShadow = '0 0 0 4px rgba(211, 124, 103, 0.1)';
                  }}
                  onBlur={(e) => {
                    e.currentTarget.style.borderColor = 'rgba(255, 255, 255, 0.1)';
                    e.currentTarget.style.background = 'rgba(255, 255, 255, 0.08)';
                    e.currentTarget.style.boxShadow = 'none';
                  }}
                />
              </div>
            </div>

            {/* Allow Questions Toggle */}
            <div style={{ marginBottom: '24px' }}>
              <label style={{
                display: 'flex',
                alignItems: 'center',
                gap: '12px',
                color: '#ffffff',
                fontSize: '16px',
                fontWeight: 600,
                cursor: 'pointer',
              }}>
                <input
                  type="checkbox"
                  checked={sessionData.allowQuestions}
                  onChange={(e) => setSessionData(prev => ({ ...prev, allowQuestions: e.target.checked }))}
                  style={{
                    width: '20px',
                    height: '20px',
                    accentColor: '#D37C67',
                  }}
                />
                <ChatBubbleIcon width="18" height="18" style={{ color: '#D37C67' }} />
                Permettre aux autres de rejoindre et poser des questions
              </label>
            </div>

            {/* Tutor Settings */}
            <div style={{ marginBottom: '24px' }}>
              <h3 style={{
                color: '#ffffff',
                fontSize: '18px',
                fontWeight: 700,
                marginBottom: '16px',
                display: 'flex',
                alignItems: 'center',
                gap: '8px',
              }}>
                <PersonIcon width="20" height="20" style={{ color: '#D37C67' }} />
                Paramètres du Tuteur
              </h3>

              <div style={{
                display: 'grid',
                gridTemplateColumns: '1fr 1fr',
                gap: '20px'
              }}>
                {/* Language Selection */}
                <div>
                  <label style={{
                    display: 'block',
                    color: '#ffffff',
                    fontSize: '16px',
                    fontWeight: 600,
                    marginBottom: '8px',
                  }}>
                    Langue
                  </label>
                  <select
                    value={sessionData.tutorLanguage}
                    onChange={(e) => setSessionData(prev => ({ ...prev, tutorLanguage: e.target.value }))}
                    style={{
                      width: '100%',
                      background: 'rgba(255, 255, 255, 0.08)',
                      backdropFilter: 'blur(10px)',
                      WebkitBackdropFilter: 'blur(10px)',
                      border: '2px solid rgba(255, 255, 255, 0.1)',
                      borderRadius: '12px',
                      color: '#ffffff',
                      fontSize: '16px',
                      fontWeight: 500,
                      padding: '12px 16px',
                      outline: 'none',
                      transition: 'all 0.3s ease',
                      fontFamily: 'inherit',
                      cursor: 'pointer',
                    }}
                    onFocus={(e) => {
                      e.currentTarget.style.borderColor = 'rgba(211, 124, 103, 0.5)';
                      e.currentTarget.style.background = 'rgba(255, 255, 255, 0.12)';
                      e.currentTarget.style.boxShadow = '0 0 0 4px rgba(211, 124, 103, 0.1)';
                    }}
                    onBlur={(e) => {
                      e.currentTarget.style.borderColor = 'rgba(255, 255, 255, 0.1)';
                      e.currentTarget.style.background = 'rgba(255, 255, 255, 0.08)';
                      e.currentTarget.style.boxShadow = 'none';
                    }}
                  >
                    {availableLanguages.map((lang) => (
                      <option key={lang.value} value={lang.value} style={{ background: '#392735', color: '#ffffff' }}>
                        {lang.label}
                      </option>
                    ))}
                  </select>
                </div>

                {/* Gender Selection */}
                <div>
                  <label style={{
                    display: 'block',
                    color: '#ffffff',
                    fontSize: '16px',
                    fontWeight: 600,
                    marginBottom: '8px',
                  }}>
                    Genre
                  </label>
                  <div style={{ display: 'flex', gap: '8px' }}>
                    {availableGenders.map((gender) => (
                      <button
                        key={gender.value}
                        onClick={() => setSessionData(prev => ({ ...prev, tutorGender: gender.value }))}
                        style={{
                          flex: 1,
                          background: sessionData.tutorGender === gender.value
                            ? 'linear-gradient(135deg, rgba(211, 124, 103, 0.3) 0%, rgba(211, 124, 103, 0.1) 100%)'
                            : 'rgba(255, 255, 255, 0.08)',
                          border: sessionData.tutorGender === gender.value
                            ? '2px solid rgba(211, 124, 103, 0.6)'
                            : '1px solid rgba(255, 255, 255, 0.1)',
                          borderRadius: '12px',
                          color: '#ffffff',
                          fontSize: '14px',
                          fontWeight: 600,
                          padding: '12px 8px',
                          cursor: 'pointer',
                          transition: 'all 0.3s ease',
                          display: 'flex',
                          flexDirection: 'column',
                          alignItems: 'center',
                          gap: '4px',
                        }}
                      >
                        <span style={{ fontSize: '18px' }}>{gender.icon}</span>
                        <span>{gender.label}</span>
                      </button>
                    ))}
                  </div>
                </div>
              </div>
            </div>

            {/* Questions Section */}
            <div style={{ marginBottom: '24px' }}>
              <div style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
                marginBottom: '12px',
              }}>
                <label style={{
                  color: '#ffffff',
                  fontSize: '16px',
                  fontWeight: 600,
                }}>
                  Questions Prédéfinies (optionnel)
                </label>
              </div>

              {/* Add Question Input */}
              <div style={{ display: 'flex', gap: '8px', marginBottom: '12px' }}>
                <input
                  type="text"
                  placeholder="Ajouter une question..."
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      handleAddQuestion(e.currentTarget.value);
                      e.currentTarget.value = '';
                    }
                  }}
                  style={{
                    flex: 1,
                    background: 'rgba(255, 255, 255, 0.08)',
                    backdropFilter: 'blur(10px)',
                    WebkitBackdropFilter: 'blur(10px)',
                    border: '2px solid rgba(255, 255, 255, 0.1)',
                    borderRadius: '12px',
                    color: '#ffffff',
                    fontSize: '14px',
                    fontWeight: 500,
                    padding: '12px 16px',
                    outline: 'none',
                    transition: 'all 0.3s ease',
                    fontFamily: 'inherit',
                  }}
                  onFocus={(e) => {
                    e.currentTarget.style.borderColor = 'rgba(211, 124, 103, 0.5)';
                    e.currentTarget.style.background = 'rgba(255, 255, 255, 0.12)';
                    e.currentTarget.style.boxShadow = '0 0 0 4px rgba(211, 124, 103, 0.1)';
                  }}
                  onBlur={(e) => {
                    e.currentTarget.style.borderColor = 'rgba(255, 255, 255, 0.1)';
                    e.currentTarget.style.background = 'rgba(255, 255, 255, 0.08)';
                    e.currentTarget.style.boxShadow = 'none';
                  }}
                />
                <button
                  onClick={(e) => {
                    const input = e.currentTarget.previousElementSibling as HTMLInputElement;
                    if (input && input.value.trim()) {
                      handleAddQuestion(input.value);
                      input.value = '';
                    }
                  }}
                  style={{
                    background: 'rgba(211, 124, 103, 0.2)',
                    border: '1px solid rgba(211, 124, 103, 0.4)',
                    borderRadius: '12px',
                    color: '#ffffff',
                    cursor: 'pointer',
                    padding: '12px 16px',
                    transition: 'all 0.3s ease',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    fontSize: '14px',
                    fontWeight: 600,
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.background = 'rgba(211, 124, 103, 0.3)';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.background = 'rgba(211, 124, 103, 0.2)';
                  }}
                >
                  Ajouter
                </button>
              </div>

              {/* Questions List */}
              {sessionData.questions.length > 0 && (
                <div style={{
                  background: 'rgba(255, 255, 255, 0.05)',
                  borderRadius: '12px',
                  padding: '12px',
                  border: '1px solid rgba(255, 255, 255, 0.1)',
                  maxHeight: '120px',
                  overflowY: 'auto',
                }}>
                  {sessionData.questions.map((question, index) => (
                    <div
                      key={index}
                      style={{
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        padding: '8px 12px',
                        background: 'rgba(255, 255, 255, 0.08)',
                        borderRadius: '8px',
                        marginBottom: '6px',
                        border: '1px solid rgba(255, 255, 255, 0.1)',
                      }}
                    >
                      <span style={{
                        color: '#ffffff',
                        fontSize: '14px',
                        flex: 1,
                      }}>
                        {question}
                      </span>
                      <button
                        onClick={() => handleRemoveQuestion(index)}
                        style={{
                          background: 'rgba(255, 255, 255, 0.1)',
                          border: '1px solid rgba(255, 255, 255, 0.2)',
                          borderRadius: '6px',
                          color: '#ffffff',
                          cursor: 'pointer',
                          padding: '4px',
                          transition: 'all 0.3s ease',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                        }}
                        onMouseEnter={(e) => {
                          e.currentTarget.style.background = 'rgba(255, 0, 0, 0.2)';
                          e.currentTarget.style.borderColor = 'rgba(255, 0, 0, 0.4)';
                        }}
                        onMouseLeave={(e) => {
                          e.currentTarget.style.background = 'rgba(255, 255, 255, 0.1)';
                          e.currentTarget.style.borderColor = 'rgba(255, 255, 255, 0.2)';
                        }}
                      >
                        <TrashIcon width="12" height="12" />
                      </button>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Resources Section */}
            <div style={{ marginBottom: '32px' }}>
              <div style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
                marginBottom: '16px',
              }}>
                <label style={{
                  color: '#ffffff',
                  fontSize: '16px',
                  fontWeight: 600,
                }}>
                  Ressources (optionnel)
                </label>
                <div style={{ display: 'flex', gap: '8px' }}>
                  {/* Add Document Button */}
                  <input
                    type="file"
                    id="document-upload"
                    accept=".pdf,.doc,.docx,.txt"
                    style={{ display: 'none' }}
                    onChange={(e) => {
                      const file = e.target.files?.[0];
                      if (file) handleAddResource(file, 'document');
                    }}
                  />
                  <button
                    onClick={() => document.getElementById('document-upload')?.click()}
                    style={{
                      background: 'rgba(255, 255, 255, 0.1)',
                      border: '1px solid rgba(255, 255, 255, 0.2)',
                      borderRadius: '10px',
                      color: '#ffffff',
                      cursor: 'pointer',
                      padding: '8px 12px',
                      transition: 'all 0.3s ease',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '6px',
                      fontSize: '14px',
                      fontWeight: 500,
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.background = 'rgba(211, 124, 103, 0.2)';
                      e.currentTarget.style.borderColor = 'rgba(211, 124, 103, 0.4)';
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.background = 'rgba(255, 255, 255, 0.1)';
                      e.currentTarget.style.borderColor = 'rgba(255, 255, 255, 0.2)';
                    }}
                  >
                    <FileTextIcon width="16" height="16" />
                    Document
                  </button>

                  {/* Add Image Button */}
                  <input
                    type="file"
                    id="image-upload"
                    accept="image/*"
                    style={{ display: 'none' }}
                    onChange={(e) => {
                      const file = e.target.files?.[0];
                      if (file) handleAddResource(file, 'image');
                    }}
                  />
                  <button
                    onClick={() => document.getElementById('image-upload')?.click()}
                    style={{
                      background: 'rgba(255, 255, 255, 0.1)',
                      border: '1px solid rgba(255, 255, 255, 0.2)',
                      borderRadius: '10px',
                      color: '#ffffff',
                      cursor: 'pointer',
                      padding: '8px 12px',
                      transition: 'all 0.3s ease',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '6px',
                      fontSize: '14px',
                      fontWeight: 500,
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.background = 'rgba(211, 124, 103, 0.2)';
                      e.currentTarget.style.borderColor = 'rgba(211, 124, 103, 0.4)';
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.background = 'rgba(255, 255, 255, 0.1)';
                      e.currentTarget.style.borderColor = 'rgba(255, 255, 255, 0.2)';
                    }}
                  >
                    <ImageIcon width="16" height="16" />
                    Image
                  </button>
                </div>
              </div>

              {/* Resources List */}
              {sessionData.resources.length > 0 && (
                <div style={{
                  background: 'rgba(255, 255, 255, 0.05)',
                  borderRadius: '16px',
                  padding: '16px',
                  border: '1px solid rgba(255, 255, 255, 0.1)',
                }}>
                  {sessionData.resources.map((resource) => (
                    <div
                      key={resource.id}
                      style={{
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        padding: '12px 16px',
                        background: 'rgba(255, 255, 255, 0.08)',
                        borderRadius: '12px',
                        marginBottom: '8px',
                        border: '1px solid rgba(255, 255, 255, 0.1)',
                      }}
                    >
                      <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                        <span style={{ color: '#D37C67', fontSize: '18px' }}>
                          {resource.type === 'image' ? <ImageIcon /> : <FileTextIcon />}
                        </span>
                        <div>
                          <div style={{
                            color: '#ffffff',
                            fontSize: '14px',
                            fontWeight: 600,
                            marginBottom: '2px',
                          }}>
                            {resource.name}
                          </div>
                          <div style={{
                            color: 'rgba(255, 255, 255, 0.6)',
                            fontSize: '12px',
                            textTransform: 'capitalize',
                          }}>
                            {resource.type}
                          </div>
                        </div>
                      </div>
                      <button
                        onClick={() => handleRemoveResource(resource.id)}
                        style={{
                          background: 'rgba(255, 255, 255, 0.1)',
                          border: '1px solid rgba(255, 255, 255, 0.2)',
                          borderRadius: '8px',
                          color: '#ffffff',
                          cursor: 'pointer',
                          padding: '6px',
                          transition: 'all 0.3s ease',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                        }}
                        onMouseEnter={(e) => {
                          e.currentTarget.style.background = 'rgba(255, 0, 0, 0.2)';
                          e.currentTarget.style.borderColor = 'rgba(255, 0, 0, 0.4)';
                        }}
                        onMouseLeave={(e) => {
                          e.currentTarget.style.background = 'rgba(255, 255, 255, 0.1)';
                          e.currentTarget.style.borderColor = 'rgba(255, 255, 255, 0.2)';
                        }}
                      >
                        <TrashIcon width="14" height="14" />
                      </button>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Action Buttons */}
            <div style={{
              display: 'flex',
              gap: '16px',
              justifyContent: 'flex-end',
              paddingTop: '24px',
              borderTop: '1px solid rgba(255, 255, 255, 0.1)',
            }}>
              {/* Cancel Button */}
              <button
                onClick={() => setShowSessionPopup(false)}
                style={{
                  background: 'rgba(255, 255, 255, 0.1)',
                  border: '2px solid rgba(255, 255, 255, 0.2)',
                  borderRadius: '16px',
                  color: '#ffffff',
                  fontSize: '16px',
                  fontWeight: 600,
                  padding: '14px 28px',
                  cursor: 'pointer',
                  transition: 'all 0.3s ease',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px',
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.background = 'rgba(255, 255, 255, 0.15)';
                  e.currentTarget.style.transform = 'translateY(-2px)';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.background = 'rgba(255, 255, 255, 0.1)';
                  e.currentTarget.style.transform = 'translateY(0)';
                }}
              >
                Annuler
              </button>

              {/* Create Button */}
              <button
                onClick={handleCreateSession}
                disabled={!sessionData.name.trim()}
                style={{
                  background: sessionData.name.trim()
                    ? 'linear-gradient(135deg, #D37C67 0%, #E8967A 100%)'
                    : 'rgba(255, 255, 255, 0.1)',
                  border: 'none',
                  borderRadius: '16px',
                  color: sessionData.name.trim() ? '#ffffff' : 'rgba(255, 255, 255, 0.5)',
                  fontSize: '16px',
                  fontWeight: 700,
                  padding: '14px 32px',
                  cursor: sessionData.name.trim() ? 'pointer' : 'not-allowed',
                  transition: 'all 0.3s ease',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px',
                  boxShadow: sessionData.name.trim()
                    ? '0 8px 25px rgba(211, 124, 103, 0.3)'
                    : 'none',
                }}
                onMouseEnter={(e) => {
                  if (sessionData.name.trim()) {
                    e.currentTarget.style.transform = 'translateY(-3px) scale(1.02)';
                    e.currentTarget.style.boxShadow = '0 12px 35px rgba(211, 124, 103, 0.4)';
                  }
                }}
                onMouseLeave={(e) => {
                  if (sessionData.name.trim()) {
                    e.currentTarget.style.transform = 'translateY(0) scale(1)';
                    e.currentTarget.style.boxShadow = '0 8px 25px rgba(211, 124, 103, 0.3)';
                  }
                }}
              >
                <CheckIcon width="18" height="18" />
                Créer la Session
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};