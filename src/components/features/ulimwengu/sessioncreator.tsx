import React, { useState } from "react";
import { Box } from "@radix-ui/themes";
import {
  Pencil1Icon,
  UploadIcon,
  ImageIcon,
  Link2Icon,
  ArrowRightIcon,
  CodeIcon,
  Cross2Icon,
  FileTextIcon,
  TrashIcon,
  PlusIcon,
  CheckIcon,
} from "@radix-ui/react-icons";

// --- Helper for action buttons ---
const actionButtons = [
  { id: "write", label: "Écrire", icon: <Pencil1Icon />, color: "#D37C67" },
  { id: "upload", label: "Télécharger", icon: <UploadIcon />, color: "#D37C67" },
  { id: "image", label: "Image", icon: <ImageIcon />, color: "#D37C67" },
  { id: "link", label: "Lien", icon: <Link2Icon />, color: "#D37C67" },
];

// --- Component Props Interface ---
export interface CreatorBoxProps {
  style?: React.CSSProperties;
  onStartSession?: (content: string) => void;
  placeholder?: string;
}

// --- The CreatorBox Component ---
export const CreatorBox = (props: CreatorBoxProps) => {
  const {
    style,
    onStartSession,
    placeholder = "Commencer un nouvel explicateur...",
  } = props;

  // --- State Management ---
  // To store the text from the textarea
  const [textValue, setTextValue] = useState("");
  // To change border color on focus for better UX
  const [isFocused, setIsFocused] = useState(false);
  // To track which action is currently active
  const [activeAction, setActiveAction] = useState("write");
  // To show/hide upload popup
  const [showUploadPopup, setShowUploadPopup] = useState(false);
  // To show/hide session creation popup
  const [showSessionPopup, setShowSessionPopup] = useState(false);
  // Session creation form data
  const [sessionData, setSessionData] = useState({
    name: '',
    description: '',
    resources: [] as Array<{id: string, name: string, type: string, file?: File}>
  });

  // --- Derived State ---
  // Check if there is any content to enable the button
  const hasContent = textValue.trim().length > 0;

  // --- Event Handlers ---
  const handleStartSession = () => {
    if (hasContent) {
      setShowSessionPopup(true);
    }
  };

  const handleCreateSession = () => {
    if (sessionData.name.trim() && onStartSession) {
      // Create session with all data
      const sessionInfo = {
        content: textValue,
        name: sessionData.name,
        description: sessionData.description,
        resources: sessionData.resources
      };
      onStartSession(JSON.stringify(sessionInfo));

      // Reset form
      setSessionData({ name: '', description: '', resources: [] });
      setTextValue('');
      setShowSessionPopup(false);
    }
  };

  const handleAddResource = (file: File, type: string) => {
    const newResource = {
      id: Date.now().toString(),
      name: file.name,
      type: type,
      file: file
    };
    setSessionData(prev => ({
      ...prev,
      resources: [...prev.resources, newResource]
    }));
  };

  const handleRemoveResource = (resourceId: string) => {
    setSessionData(prev => ({
      ...prev,
      resources: prev.resources.filter(r => r.id !== resourceId)
    }));
  };

  const handleActionClick = (actionId: string) => {
    setActiveAction(actionId);
    if (actionId === "upload") {
      setShowUploadPopup(true);
    }
  };

  const handleUploadOption = (option: string) => {
    console.log(`Option sélectionnée: ${option}`);
    setShowUploadPopup(false);
    // Here you would implement the actual upload logic
  };

  // --- Component Styles ---
  // Beautiful glassy design with gradient and glassmorphism
  const containerStyle: React.CSSProperties = {
    background: isFocused
      ? "linear-gradient(135deg, rgba(57, 39, 53, 0.95) 0%, rgba(211, 124, 103, 0.15) 100%)"
      : "linear-gradient(135deg, rgba(57, 39, 53, 0.85) 0%, rgba(57, 39, 53, 0.65) 100%)",
    backdropFilter: "blur(20px)",
    WebkitBackdropFilter: "blur(20px)",
    borderRadius: "24px",
    border: isFocused
      ? "1px solid rgba(211, 124, 103, 0.4)"
      : "1px solid rgba(255, 255, 255, 0.1)",
    padding: "32px",
    marginBottom: "20px",
    boxShadow: isFocused
      ? "0 20px 40px rgba(211, 124, 103, 0.2), 0 8px 32px rgba(0, 0, 0, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.1)"
      : "0 16px 32px rgba(0, 0, 0, 0.2), 0 8px 16px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.05)",
    transition: "all 0.4s cubic-bezier(0.4, 0, 0.2, 1)",
    width: '100%',
    position: 'relative',
    overflow: 'hidden',
    ...style,
  };

  const textareaStyle: React.CSSProperties = {
    width: "100%",
    minHeight: "60px",
    background: "rgba(255, 255, 255, 0.05)",
    backdropFilter: "blur(10px)",
    WebkitBackdropFilter: "blur(10px)",
    border: "1px solid rgba(255, 255, 255, 0.1)",
    borderRadius: "16px",
    outline: "none",
    resize: "none",
    fontSize: "18px",
    fontWeight: 500,
    color: "#ffffff",
    marginBottom: "24px",
    padding: "16px 20px",
    transition: "all 0.3s ease",
    fontFamily: "inherit",
    lineHeight: "1.5",
  };

  const actionsContainerStyle: React.CSSProperties = {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
    flexWrap: 'wrap',
    gap: '20px',
    marginTop: "8px",
  };

  const actionButtonStyle: React.CSSProperties = {
    display: "flex",
    alignItems: "center",
    gap: "10px",
    background: "rgba(255, 255, 255, 0.08)",
    backdropFilter: "blur(10px)",
    WebkitBackdropFilter: "blur(10px)",
    border: "1px solid rgba(255, 255, 255, 0.1)",
    borderRadius: "12px",
    cursor: "pointer",
    color: "#ffffff",
    fontSize: "14px",
    fontWeight: 500,
    padding: "12px 16px",
    transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
    position: 'relative',
    overflow: 'hidden',
  };

  return (
    <>
      {/* Custom CSS for placeholder styling and animations */}
      <style jsx>{`
        textarea::placeholder {
          color: rgba(255, 255, 255, 0.5);
          font-style: italic;
          font-weight: 400;
        }
        textarea:focus::placeholder {
          color: rgba(211, 124, 103, 0.7);
        }
        @keyframes fadeIn {
          from {
            opacity: 0;
            transform: scale(0.9);
          }
          to {
            opacity: 1;
            transform: scale(1);
          }
        }
      `}</style>

      <Box style={containerStyle}>
        {/* Decorative gradient overlay */}
        <div style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          height: '2px',
          background: 'linear-gradient(90deg, transparent 0%, #D37C67 50%, transparent 100%)',
          opacity: isFocused ? 1 : 0.3,
          transition: 'opacity 0.3s ease',
        }} />

      <textarea
        style={{
          ...textareaStyle,
          background: isFocused
            ? "rgba(255, 255, 255, 0.1)"
            : "rgba(255, 255, 255, 0.05)",
          borderColor: isFocused
            ? "rgba(211, 124, 103, 0.3)"
            : "rgba(255, 255, 255, 0.1)",
          boxShadow: isFocused
            ? "0 8px 32px rgba(211, 124, 103, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.1)"
            : "0 4px 16px rgba(0, 0, 0, 0.1)",
          transform: isFocused ? 'scale(1.01)' : 'scale(1)',
        }}
        placeholder={placeholder}
        value={textValue}
        onChange={(e) => setTextValue(e.target.value)}
        onFocus={() => setIsFocused(true)}
        onBlur={() => setIsFocused(false)}
        rows={3}
      />

      <div style={actionsContainerStyle}>
        {/* Left side: Action Buttons */}
        <div style={{ display: "flex", alignItems: "center", gap: "12px", flexWrap: 'wrap' }}>
          {actionButtons.map((action) => {
            const isActive = activeAction === action.id;
            return (
              <button
                key={action.id}
                title={action.label}
                style={{
                  ...actionButtonStyle,
                  background: isActive
                    ? 'rgba(211, 124, 103, 0.3)'
                    : 'rgba(255, 255, 255, 0.08)',
                  borderColor: isActive
                    ? 'rgba(211, 124, 103, 0.5)'
                    : 'rgba(255, 255, 255, 0.1)',
                  boxShadow: isActive
                    ? '0 4px 20px rgba(211, 124, 103, 0.3)'
                    : 'none',
                }}
                onClick={() => handleActionClick(action.id)}
                onMouseEnter={(e) => {
                  if (!isActive) {
                    e.currentTarget.style.background = 'rgba(211, 124, 103, 0.2)';
                    e.currentTarget.style.borderColor = 'rgba(211, 124, 103, 0.4)';
                    e.currentTarget.style.transform = 'translateY(-2px)';
                    e.currentTarget.style.boxShadow = '0 8px 25px rgba(211, 124, 103, 0.2)';
                  }
                }}
                onMouseLeave={(e) => {
                  if (!isActive) {
                    e.currentTarget.style.background = 'rgba(255, 255, 255, 0.08)';
                    e.currentTarget.style.borderColor = 'rgba(255, 255, 255, 0.1)';
                    e.currentTarget.style.transform = 'translateY(0)';
                    e.currentTarget.style.boxShadow = 'none';
                  }
                }}
              >
                <span style={{ color: action.color, fontSize: '16px' }}>{action.icon}</span>
                <span style={{ fontSize: '13px', fontWeight: 600 }}>{action.label}</span>
              </button>
            );
          })}
        </div>

        {/* Right side: Conditional "Start Session" Button */}
        {hasContent && (
          <button
            style={{
              background: 'linear-gradient(135deg, #D37C67 0%, #E8967A 100%)',
              border: 'none',
              borderRadius: '16px',
              color: '#ffffff',
              fontSize: '16px',
              fontWeight: 700,
              padding: '16px 32px',
              cursor: 'pointer',
              transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
              boxShadow: '0 8px 25px rgba(211, 124, 103, 0.3)',
              display: 'flex',
              alignItems: 'center',
              gap: '8px',
              position: 'relative',
              overflow: 'hidden',
            }}
            onClick={handleStartSession}
            onMouseEnter={(e) => {
              e.currentTarget.style.transform = 'translateY(-3px) scale(1.02)';
              e.currentTarget.style.boxShadow = '0 12px 35px rgba(211, 124, 103, 0.4)';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.transform = 'translateY(0) scale(1)';
              e.currentTarget.style.boxShadow = '0 8px 25px rgba(211, 124, 103, 0.3)';
            }}
          >
            <span>Commencer la Session</span>
            <ArrowRightIcon style={{ fontSize: '18px' }} />
          </button>
        )}
      </div>

      {/* Upload Options Popup */}
     
    </Box>
    {showUploadPopup && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'rgba(0, 0, 0, 0.7)',
          backdropFilter: 'blur(10px)',
          WebkitBackdropFilter: 'blur(10px)',
          zIndex: 1000,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          animation: 'fadeIn 0.3s ease',
        }}>
          <div style={{
            background: 'linear-gradient(135deg, rgba(57, 39, 53, 0.95) 0%, rgba(57, 39, 53, 0.85) 100%)',
            backdropFilter: 'blur(20px)',
            WebkitBackdropFilter: 'blur(20px)',
            borderRadius: '24px',
            border: '1px solid rgba(255, 255, 255, 0.1)',
            padding: '32px',
            minWidth: '400px',
            boxShadow: '0 20px 40px rgba(0, 0, 0, 0.3)',
            position: 'relative',
          }}>
            {/* Close button */}
            <button
              style={{
                position: 'absolute',
                top: '16px',
                right: '16px',
                background: 'rgba(255, 255, 255, 0.1)',
                border: '1px solid rgba(255, 255, 255, 0.2)',
                borderRadius: '8px',
                color: '#ffffff',
                cursor: 'pointer',
                padding: '8px',
                transition: 'all 0.2s ease',
              }}
              onClick={() => setShowUploadPopup(false)}
              onMouseEnter={(e) => {
                e.currentTarget.style.background = 'rgba(211, 124, 103, 0.3)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.background = 'rgba(255, 255, 255, 0.1)';
              }}
            >
              <Cross2Icon />
            </button>

            <h3 style={{
              color: '#ffffff',
              fontSize: '24px',
              fontWeight: 700,
              marginBottom: '24px',
              textAlign: 'center',
            }}>
              Options de Téléchargement
            </h3>

            <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
              {[
                { id: 'file', label: 'Télécharger un fichier', icon: <UploadIcon /> },
                { id: 'google-drive', label: 'Depuis Google Drive', icon: <Link2Icon /> },
                { id: 'import-code', label: 'Importer du code', icon: <CodeIcon /> },
              ].map((option) => (
                <button
                  key={option.id}
                  style={{
                    background: 'rgba(255, 255, 255, 0.08)',
                    backdropFilter: 'blur(10px)',
                    WebkitBackdropFilter: 'blur(10px)',
                    border: '1px solid rgba(255, 255, 255, 0.1)',
                    borderRadius: '16px',
                    color: '#ffffff',
                    fontSize: '16px',
                    fontWeight: 600,
                    padding: '16px 24px',
                    cursor: 'pointer',
                    transition: 'all 0.3s ease',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '12px',
                    width: '100%',
                  }}
                  onClick={() => handleUploadOption(option.id)}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.background = 'rgba(211, 124, 103, 0.2)';
                    e.currentTarget.style.borderColor = 'rgba(211, 124, 103, 0.4)';
                    e.currentTarget.style.transform = 'translateY(-2px)';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.background = 'rgba(255, 255, 255, 0.08)';
                    e.currentTarget.style.borderColor = 'rgba(255, 255, 255, 0.1)';
                    e.currentTarget.style.transform = 'translateY(0)';
                  }}
                >
                  <span style={{ color: '#D37C67', fontSize: '20px' }}>{option.icon}</span>
                  <span>{option.label}</span>
                </button>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Session Creation Popup */}
      {showSessionPopup && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'rgba(0, 0, 0, 0.8)',
          backdropFilter: 'blur(15px)',
          WebkitBackdropFilter: 'blur(15px)',
          zIndex: 1001,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          animation: 'fadeIn 0.3s ease',
        }}>
          <div style={{
            background: 'linear-gradient(135deg, rgba(57, 39, 53, 0.98) 0%, rgba(57, 39, 53, 0.95) 100%)',
            backdropFilter: 'blur(25px)',
            WebkitBackdropFilter: 'blur(25px)',
            borderRadius: '28px',
            border: '1px solid rgba(211, 124, 103, 0.3)',
            padding: '40px',
            minWidth: '500px',
            maxWidth: '600px',
            maxHeight: '80vh',
            overflowY: 'auto',
            boxShadow: '0 25px 50px rgba(0, 0, 0, 0.4), 0 0 0 1px rgba(255, 255, 255, 0.05)',
            position: 'relative',
          }}>
            {/* Decorative gradient overlay */}
            <div style={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              height: '4px',
              background: 'linear-gradient(90deg, transparent 0%, #D37C67 50%, transparent 100%)',
              borderRadius: '28px 28px 0 0',
            }} />

            {/* Close button */}
            <button
              style={{
                position: 'absolute',
                top: '20px',
                right: '20px',
                background: 'rgba(255, 255, 255, 0.1)',
                border: '1px solid rgba(255, 255, 255, 0.2)',
                borderRadius: '12px',
                color: '#ffffff',
                cursor: 'pointer',
                padding: '12px',
                transition: 'all 0.3s ease',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
              onClick={() => setShowSessionPopup(false)}
              onMouseEnter={(e) => {
                e.currentTarget.style.background = 'rgba(211, 124, 103, 0.3)';
                e.currentTarget.style.transform = 'scale(1.1)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.background = 'rgba(255, 255, 255, 0.1)';
                e.currentTarget.style.transform = 'scale(1)';
              }}
            >
              <Cross2Icon width="18" height="18" />
            </button>

            {/* Header */}
            <div style={{ marginBottom: '32px', textAlign: 'center' }}>
              <h2 style={{
                color: '#ffffff',
                fontSize: '28px',
                fontWeight: 800,
                marginBottom: '8px',
                background: 'linear-gradient(135deg, #ffffff 0%, #D37C67 100%)',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                backgroundClip: 'text',
              }}>
                Créer une Nouvelle Session
              </h2>
              <p style={{
                color: 'rgba(255, 255, 255, 0.7)',
                fontSize: '16px',
                margin: 0,
                lineHeight: 1.5,
              }}>
                Configurez votre session d&apos;apprentissage personnalisée
              </p>
            </div>

            {/* Session Name Input */}
            <div style={{ marginBottom: '24px' }}>
              <label style={{
                display: 'block',
                color: '#ffffff',
                fontSize: '16px',
                fontWeight: 600,
                marginBottom: '8px',
              }}>
                Nom de la Session *
              </label>
              <input
                type="text"
                placeholder="Ex: Entretien d'embauche avec Luna"
                value={sessionData.name}
                onChange={(e) => setSessionData(prev => ({ ...prev, name: e.target.value }))}
                style={{
                  width: '100%',
                  background: 'rgba(255, 255, 255, 0.08)',
                  backdropFilter: 'blur(10px)',
                  WebkitBackdropFilter: 'blur(10px)',
                  border: '2px solid rgba(255, 255, 255, 0.1)',
                  borderRadius: '16px',
                  color: '#ffffff',
                  fontSize: '16px',
                  fontWeight: 500,
                  padding: '16px 20px',
                  outline: 'none',
                  transition: 'all 0.3s ease',
                  fontFamily: 'inherit',
                }}
                onFocus={(e) => {
                  e.currentTarget.style.borderColor = 'rgba(211, 124, 103, 0.5)';
                  e.currentTarget.style.background = 'rgba(255, 255, 255, 0.12)';
                  e.currentTarget.style.boxShadow = '0 0 0 4px rgba(211, 124, 103, 0.1)';
                }}
                onBlur={(e) => {
                  e.currentTarget.style.borderColor = 'rgba(255, 255, 255, 0.1)';
                  e.currentTarget.style.background = 'rgba(255, 255, 255, 0.08)';
                  e.currentTarget.style.boxShadow = 'none';
                }}
              />
            </div>

            {/* Session Description Input */}
            <div style={{ marginBottom: '32px' }}>
              <label style={{
                display: 'block',
                color: '#ffffff',
                fontSize: '16px',
                fontWeight: 600,
                marginBottom: '8px',
              }}>
                Description (optionnel)
              </label>
              <textarea
                placeholder="Décrivez brièvement l'objectif de cette session..."
                value={sessionData.description}
                onChange={(e) => setSessionData(prev => ({ ...prev, description: e.target.value }))}
                rows={3}
                style={{
                  width: '100%',
                  background: 'rgba(255, 255, 255, 0.08)',
                  backdropFilter: 'blur(10px)',
                  WebkitBackdropFilter: 'blur(10px)',
                  border: '2px solid rgba(255, 255, 255, 0.1)',
                  borderRadius: '16px',
                  color: '#ffffff',
                  fontSize: '16px',
                  fontWeight: 500,
                  padding: '16px 20px',
                  outline: 'none',
                  resize: 'vertical',
                  minHeight: '80px',
                  transition: 'all 0.3s ease',
                  fontFamily: 'inherit',
                  lineHeight: 1.5,
                }}
                onFocus={(e) => {
                  e.currentTarget.style.borderColor = 'rgba(211, 124, 103, 0.5)';
                  e.currentTarget.style.background = 'rgba(255, 255, 255, 0.12)';
                  e.currentTarget.style.boxShadow = '0 0 0 4px rgba(211, 124, 103, 0.1)';
                }}
                onBlur={(e) => {
                  e.currentTarget.style.borderColor = 'rgba(255, 255, 255, 0.1)';
                  e.currentTarget.style.background = 'rgba(255, 255, 255, 0.08)';
                  e.currentTarget.style.boxShadow = 'none';
                }}
              />
            </div>

            {/* Resources Section */}
            <div style={{ marginBottom: '32px' }}>
              <div style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
                marginBottom: '16px',
              }}>
                <label style={{
                  color: '#ffffff',
                  fontSize: '16px',
                  fontWeight: 600,
                }}>
                  Ressources (optionnel)
                </label>
                <div style={{ display: 'flex', gap: '8px' }}>
                  {/* Add Document Button */}
                  <input
                    type="file"
                    id="document-upload"
                    accept=".pdf,.doc,.docx,.txt"
                    style={{ display: 'none' }}
                    onChange={(e) => {
                      const file = e.target.files?.[0];
                      if (file) handleAddResource(file, 'document');
                    }}
                  />
                  <button
                    onClick={() => document.getElementById('document-upload')?.click()}
                    style={{
                      background: 'rgba(255, 255, 255, 0.1)',
                      border: '1px solid rgba(255, 255, 255, 0.2)',
                      borderRadius: '10px',
                      color: '#ffffff',
                      cursor: 'pointer',
                      padding: '8px 12px',
                      transition: 'all 0.3s ease',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '6px',
                      fontSize: '14px',
                      fontWeight: 500,
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.background = 'rgba(211, 124, 103, 0.2)';
                      e.currentTarget.style.borderColor = 'rgba(211, 124, 103, 0.4)';
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.background = 'rgba(255, 255, 255, 0.1)';
                      e.currentTarget.style.borderColor = 'rgba(255, 255, 255, 0.2)';
                    }}
                  >
                    <FileTextIcon width="16" height="16" />
                    Document
                  </button>

                  {/* Add Image Button */}
                  <input
                    type="file"
                    id="image-upload"
                    accept="image/*"
                    style={{ display: 'none' }}
                    onChange={(e) => {
                      const file = e.target.files?.[0];
                      if (file) handleAddResource(file, 'image');
                    }}
                  />
                  <button
                    onClick={() => document.getElementById('image-upload')?.click()}
                    style={{
                      background: 'rgba(255, 255, 255, 0.1)',
                      border: '1px solid rgba(255, 255, 255, 0.2)',
                      borderRadius: '10px',
                      color: '#ffffff',
                      cursor: 'pointer',
                      padding: '8px 12px',
                      transition: 'all 0.3s ease',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '6px',
                      fontSize: '14px',
                      fontWeight: 500,
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.background = 'rgba(211, 124, 103, 0.2)';
                      e.currentTarget.style.borderColor = 'rgba(211, 124, 103, 0.4)';
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.background = 'rgba(255, 255, 255, 0.1)';
                      e.currentTarget.style.borderColor = 'rgba(255, 255, 255, 0.2)';
                    }}
                  >
                    <ImageIcon width="16" height="16" />
                    Image
                  </button>
                </div>
              </div>

              {/* Resources List */}
              {sessionData.resources.length > 0 && (
                <div style={{
                  background: 'rgba(255, 255, 255, 0.05)',
                  borderRadius: '16px',
                  padding: '16px',
                  border: '1px solid rgba(255, 255, 255, 0.1)',
                }}>
                  {sessionData.resources.map((resource) => (
                    <div
                      key={resource.id}
                      style={{
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        padding: '12px 16px',
                        background: 'rgba(255, 255, 255, 0.08)',
                        borderRadius: '12px',
                        marginBottom: '8px',
                        border: '1px solid rgba(255, 255, 255, 0.1)',
                      }}
                    >
                      <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                        <span style={{ color: '#D37C67', fontSize: '18px' }}>
                          {resource.type === 'image' ? <ImageIcon /> : <FileTextIcon />}
                        </span>
                        <div>
                          <div style={{
                            color: '#ffffff',
                            fontSize: '14px',
                            fontWeight: 600,
                            marginBottom: '2px',
                          }}>
                            {resource.name}
                          </div>
                          <div style={{
                            color: 'rgba(255, 255, 255, 0.6)',
                            fontSize: '12px',
                            textTransform: 'capitalize',
                          }}>
                            {resource.type}
                          </div>
                        </div>
                      </div>
                      <button
                        onClick={() => handleRemoveResource(resource.id)}
                        style={{
                          background: 'rgba(255, 255, 255, 0.1)',
                          border: '1px solid rgba(255, 255, 255, 0.2)',
                          borderRadius: '8px',
                          color: '#ffffff',
                          cursor: 'pointer',
                          padding: '6px',
                          transition: 'all 0.3s ease',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                        }}
                        onMouseEnter={(e) => {
                          e.currentTarget.style.background = 'rgba(255, 0, 0, 0.2)';
                          e.currentTarget.style.borderColor = 'rgba(255, 0, 0, 0.4)';
                        }}
                        onMouseLeave={(e) => {
                          e.currentTarget.style.background = 'rgba(255, 255, 255, 0.1)';
                          e.currentTarget.style.borderColor = 'rgba(255, 255, 255, 0.2)';
                        }}
                      >
                        <TrashIcon width="14" height="14" />
                      </button>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Action Buttons */}
            <div style={{
              display: 'flex',
              gap: '16px',
              justifyContent: 'flex-end',
              paddingTop: '24px',
              borderTop: '1px solid rgba(255, 255, 255, 0.1)',
            }}>
              {/* Cancel Button */}
              <button
                onClick={() => setShowSessionPopup(false)}
                style={{
                  background: 'rgba(255, 255, 255, 0.1)',
                  border: '2px solid rgba(255, 255, 255, 0.2)',
                  borderRadius: '16px',
                  color: '#ffffff',
                  fontSize: '16px',
                  fontWeight: 600,
                  padding: '14px 28px',
                  cursor: 'pointer',
                  transition: 'all 0.3s ease',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px',
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.background = 'rgba(255, 255, 255, 0.15)';
                  e.currentTarget.style.transform = 'translateY(-2px)';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.background = 'rgba(255, 255, 255, 0.1)';
                  e.currentTarget.style.transform = 'translateY(0)';
                }}
              >
                Annuler
              </button>

              {/* Create Button */}
              <button
                onClick={handleCreateSession}
                disabled={!sessionData.name.trim()}
                style={{
                  background: sessionData.name.trim()
                    ? 'linear-gradient(135deg, #D37C67 0%, #E8967A 100%)'
                    : 'rgba(255, 255, 255, 0.1)',
                  border: 'none',
                  borderRadius: '16px',
                  color: sessionData.name.trim() ? '#ffffff' : 'rgba(255, 255, 255, 0.5)',
                  fontSize: '16px',
                  fontWeight: 700,
                  padding: '14px 32px',
                  cursor: sessionData.name.trim() ? 'pointer' : 'not-allowed',
                  transition: 'all 0.3s ease',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px',
                  boxShadow: sessionData.name.trim()
                    ? '0 8px 25px rgba(211, 124, 103, 0.3)'
                    : 'none',
                }}
                onMouseEnter={(e) => {
                  if (sessionData.name.trim()) {
                    e.currentTarget.style.transform = 'translateY(-3px) scale(1.02)';
                    e.currentTarget.style.boxShadow = '0 12px 35px rgba(211, 124, 103, 0.4)';
                  }
                }}
                onMouseLeave={(e) => {
                  if (sessionData.name.trim()) {
                    e.currentTarget.style.transform = 'translateY(0) scale(1)';
                    e.currentTarget.style.boxShadow = '0 8px 25px rgba(211, 124, 103, 0.3)';
                  }
                }}
              >
                <CheckIcon width="18" height="18" />
                Créer la Session
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};