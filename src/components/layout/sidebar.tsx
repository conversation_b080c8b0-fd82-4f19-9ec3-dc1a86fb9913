import React, { useEffect, useCallback } from "react";
import { <PERSON>ton, Avatar, Heading, Text } from "@radix-ui/themes";
import { useRouter, usePathname } from "next/navigation";
import Link from "next/link";
import {
  MagnifyingGlassIcon,
  LayersIcon,
  PersonIcon,
  HomeIcon,
  ChatBubbleIcon,
  BookmarkIcon,
  FileTextIcon,
  VideoIcon,
  CalendarIcon,
  GearIcon
} from "@radix-ui/react-icons";
import Logo from "./logo";

// --- Data for the sidebar items with routes ---
const sidebarItems = [
  {
    icon: <HomeIcon />,
    label: "Home",
    route: "/ulimwengu",
    // subsections: [...]
  },
  {
    icon: <LayersIcon />,
    label: "Sessions",
    route: "",
    subsections: [
      { label: "Active Sessions", icon: <VideoIcon />, route: "/ulimwengu/session/active/ssss" },
      { label: "Scheduled", icon: <CalendarIcon />, route: "/ulimwengu/sessions/scheduled" },
      { label: "Sessions Passées", icon: <BookmarkIcon />, route: "/ulimwengu/sessions/past" },
    ]
  },
  {
    icon: <GearIcon />,
    label: "Settings",
    route: "",
    subsections: [
      { label: "Profile", icon: <PersonIcon />, route: "/ulimwengu/settings/profile" },
      { label: "Language", icon: <PersonIcon />, route: "/ulimwengu/settings/language" },
      { label: "Settings", icon: <MagnifyingGlassIcon />, route: "/ulimwengu/settings/general" },
      { label: "Preferences", icon: <FileTextIcon />, route: "/ulimwengu/settings/preferences" },
      { label: "Help & Support", icon: <ChatBubbleIcon />, route: "/ulimwengu/settings/support" }
    ]
  },
];

export default function Sidebar() {
  const router = useRouter();
  const pathname = usePathname();
  
  // Determine which main section is active based on the current path
  const getActiveMainSection = useCallback(() => {
    // Default to Home if no match
    let activeSection = "Home";

    // Check if the current path matches any main section or subsection
    for (const item of sidebarItems) {
      // Check if path starts with the main route
      if (pathname === item.route || pathname.startsWith(`${item.route}/`)) {
        activeSection = item.label;
        break;
      }

      // Check subsections
      if (item.subsections) {
        const matchingSubsection = item.subsections.find(
          sub => pathname === sub.route || pathname.startsWith(`${sub.route}/`)
        );
        if (matchingSubsection) {
          activeSection = item.label;
          break;
        }
      }
    }

    return activeSection;
  }, [pathname]);

  // State to track the label of the selected main section
  const [selectedLabel, setSelectedLabel] = React.useState<string | null>(getActiveMainSection());

  // Update selected label when path changes
  useEffect(() => {
    setSelectedLabel(getActiveMainSection());
  }, [pathname, getActiveMainSection]);

  // Find the full item object for the selected section
  const selectedItem = sidebarItems.find(item => item.label === selectedLabel);

  return (
    <div style={{ display: "flex", height: "100vh", position: "sticky", top: 0 }}>
      {/* --- Primary Sidebar (Icons) --- */}
      <aside
        style={{
          position: "relative",
          width: "80px",
          height: "100vh",
          top: 0,
          left: 0,
          background: "linear-gradient(180deg, #392735 0%, #2a1f1d 100%)",
          boxShadow: "2px 0 16px rgba(0,0,0,0.08)",
          zIndex: 1100,
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          padding: "0 8px",
          transition: "width 0.3s ease",
        }}
        aria-label="Primary Sidebar"
      >
        {/* Logo */}
        <div style={{
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          padding: "20px 0",
          height: "64px",
          width: '100%',
          borderBottom: "1px solid rgba(255, 255, 255, 0.1)"
        }}>
          <Logo islight={true} />
        </div>
        
        {/* Menu Items */}
        <nav style={{ flex: 1, marginTop: 20, width: '100%' }}>
          {sidebarItems.map((item) => (
            <div key={item.label} style={{ marginBottom: "16px" }}>
              {item.route ? (
                <Link href={item.route} style={{ textDecoration: 'none' }}>
                  <Button
                    variant="ghost"
                    style={{
                      width: "90%",
                      height: "64px",
                      display: "flex",
                      flexDirection: "column",
                      alignItems: "center",
                      justifyContent: "center",
                      gap: "4px",
                      cursor: "pointer",
                      transition: "all 0.2s ease",
                      backgroundColor: selectedLabel === item.label ? "#D37C67" : "transparent",
                      color: selectedLabel === item.label ? "#ffffff" : "#ffffff",
                      border: selectedLabel === item.label ? "1px solid #D37C67" : "1px solid transparent",
                    }}
                    size="3"
                    title={item.label} // Tooltip for accessibility
                    onClick={(e) => {
                      e.preventDefault(); // Prevent default to handle navigation manually

                      // Toggle selection: if already selected, unselect; otherwise, select.
                      if (selectedLabel === item.label) {
                        setSelectedLabel(null);
                        // Only navigate if the item has a route
                        if (item.route) {
                          router.push(item.route);
                        }
                      } else {
                        setSelectedLabel(item.label);
                        // Only navigate if the item has a route
                        if (item.route) {
                          router.push(item.route);
                        }
                      }
                    }}
                    onMouseEnter={(e) => {
                      if (selectedLabel !== item.label) {
                        e.currentTarget.style.backgroundColor = "rgba(211, 124, 103, 0.2)";
                        e.currentTarget.style.borderColor = "rgba(211, 124, 103, 0.3)";
                      }
                    }}
                    onMouseLeave={(e) => {
                      if (selectedLabel !== item.label) {
                        e.currentTarget.style.backgroundColor = "transparent";
                        e.currentTarget.style.borderColor = "transparent";
                      }
                    }}
                  >
                    <div style={{ fontSize: "20px" }}>{item.icon}</div>
                    <span style={{ fontSize: "10px", fontWeight: 500, textAlign: "center", lineHeight: "12px" }}>
                      {item.label}
                    </span>
                  </Button>
                </Link>
              ) : (
                <Button
                  variant="ghost"
                  style={{
                    width: "90%",
                    height: "64px",
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "center",
                    justifyContent: "center",
                    gap: "4px",
                    cursor: "pointer",
                    transition: "all 0.2s ease",
                    backgroundColor: selectedLabel === item.label ? "#D37C67" : "transparent",
                    color: selectedLabel === item.label ? "#ffffff" : "#ffffff",
                    border: selectedLabel === item.label ? "1px solid #D37C67" : "1px solid transparent",
                  }}
                  size="3"
                  title={item.label} // Tooltip for accessibility
                  onClick={(e) => {
                    e.preventDefault(); // Prevent default to handle navigation manually

                    // Toggle selection: if already selected, unselect; otherwise, select.
                    if (selectedLabel === item.label) {
                      setSelectedLabel(null);
                    } else {
                      setSelectedLabel(item.label);
                    }
                  }}
                  onMouseEnter={(e) => {
                    if (selectedLabel !== item.label) {
                      e.currentTarget.style.backgroundColor = "rgba(211, 124, 103, 0.2)";
                      e.currentTarget.style.borderColor = "rgba(211, 124, 103, 0.3)";
                    }
                  }}
                  onMouseLeave={(e) => {
                    if (selectedLabel !== item.label) {
                      e.currentTarget.style.backgroundColor = "transparent";
                      e.currentTarget.style.borderColor = "transparent";
                    }
                  }}
                >
                  <div style={{ fontSize: "20px" }}>{item.icon}</div>
                  <span style={{ fontSize: "10px", fontWeight: 500, textAlign: "center", lineHeight: "12px" }}>
                    {item.label}
                  </span>
                </Button>
              )}
            </div>
          ))}
        </nav>
        
        {/* Bottom Section - Avatar */}
        <div style={{ padding: "16px 0", marginTop: "auto", borderTop: "1px solid rgba(255, 255, 255, 0.1)", width: '100%', display: 'flex', justifyContent: 'center' }}>
          <Avatar
            fallback="A"
            size="3"
            radius="full"
            src="/profile.jpg"
            alt="Account"
            style={{ cursor: 'pointer' }}
          />
        </div>
      </aside>

      {/* --- Secondary Panel (Subsections) --- */}
      {selectedItem && selectedItem.subsections && (
        <aside
          style={{
            width: "240px",
            height: "100vh",
            boxShadow: "4px 0 16px rgba(0,0,0,0.05)",
            zIndex: 1000,
            display: "flex",
            flexDirection: "column",
            padding: "0 16px",
            borderRight: "1px solid rgba(211, 124, 103, 0.2)"
          }}
          aria-label="Subsections Panel"
        >
          {/* Header of the subsection panel */}
          <div style={{ height: '64px', display: 'flex', alignItems: 'center', padding: '20px 0', borderBottom: '1px solid rgba(255, 255, 255, 0.1)' }}>
            <Heading as="h2" size="4" weight="bold" style={{ color: "#000" }}>{selectedItem.label}</Heading>
          </div>
          
          {/* List of subsections */}
          <nav style={{ flex: 1, marginTop: 20 }}>
            {selectedItem.subsections.map((subsection) => {
              const isActive = pathname === subsection.route || pathname.startsWith(`${subsection.route}/`);
              
              return (
                <Link key={subsection.label} href={subsection.route} style={{ textDecoration: 'none' }}>
                  <Button
                    variant="ghost"
                    style={{
                      width: "100%",
                      height: "40px",
                      justifyContent: "flex-start",
                      fontWeight: isActive ? 600 : 500,
                      fontSize: "14px",
                      marginBottom: "4px",
                      padding: "8px 12px",
                      display: "flex",
                      alignItems: "center",
                      gap: "12px",
                      backgroundColor: isActive ? "rgba(211, 124, 103, 0.2)" : "transparent",
                      color: isActive ? "#D37C67" : "#000",
                      border: isActive ? "1px solid rgba(211, 124, 103, 0.3)" : "1px solid transparent",
                      transition: "all 0.2s ease"
                    }}
                    size="2"
                    onMouseEnter={(e) => {
                      if (!isActive) {
                        e.currentTarget.style.backgroundColor = "rgba(211, 124, 103, 0.1)";
                        e.currentTarget.style.borderColor = "rgba(211, 124, 103, 0.2)";
                      }
                    }}
                    onMouseLeave={(e) => {
                      if (!isActive) {
                        e.currentTarget.style.backgroundColor = "transparent";
                        e.currentTarget.style.borderColor = "transparent";
                      }
                    }}
                  >
                    <div style={{ fontSize: "16px", color: "#D37C67" }}>{subsection.icon}</div>
                    <Text as="span" style={{ color: isActive ? "#D37C67" : "#000" }}>{subsection.label}</Text>
                  </Button>
                </Link>
              );
            })}
          </nav>
        </aside>
      )}
    </div>
  );
}
